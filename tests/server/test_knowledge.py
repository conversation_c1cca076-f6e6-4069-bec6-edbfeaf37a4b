import pytest
import uuid
from tests.server.server_basic import (
    client, get_test_headers, generate_test_document, generate_test_chunk_config,
    check_success_response, check_error_response
)
from infra.memory.knowledge_operator import KnowledgeOperator
knowledge_operator = KnowledgeOperator.get_instance("test_app")

assert knowledge_operator.sync_client.ping()

# 测试环境设置：只在需要时设置索引参数
def ensure_index_settings(index_name="knowledge-base"):
    """确保指定索引有正确的设置"""
    try:
        knowledge_operator.sync_client.indices.put_settings(
            index=index_name,
            body={"index.max_result_window": "100000"},
            preserve_existing=True
        )
        print(f"Ensured max_result_window setting for {index_name}")
    except Exception as e:
        print(f"Note: Could not set index settings for {index_name}: {e}")
        # 在测试环境中，这通常不是问题

# 不在这里强制设置，而是在需要时调用

class TestKnowledgeEndpoints:
    """知识库接口测试类"""

    def test_query_knowledge_list_basic(self):
        """测试基本的知识库列表查询"""
        # 确保索引设置正确
        ensure_index_settings(index_name=knowledge_operator.config.index_name)
        
        headers = get_test_headers()
        params = {
            "Filters": [],
            "Sorts": [],
            "Page": 1,
            "PageSize": 10
        }
        
        response = client.post("/query_knowledge_list", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "KnowledgeInfoList" in data
        assert "Total" in data
        assert isinstance(data["KnowledgeInfoList"], list)

    def test_query_knowledge_list_with_filters(self):
        """测试带过滤条件的知识库列表查询"""
        headers = get_test_headers()
        params = {
            "Filters": [
                {
                    "Field": "Status",
                    "Op": "eq",
                    "Value": ["1"]
                }
            ],
            "Sorts": [],
            "Page": 1,
            "PageSize": 10
        }
        
        response = client.post("/query_knowledge_list", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "KnowledgeInfoList" in data

    def test_query_knowledge_list_with_sorts(self):
        """测试带排序条件的知识库列表查询"""
        headers = get_test_headers()
        params = {
            "Filters": [],
            "Sorts": [
                {
                    "Field": "CreateTime",
                    "Desc": True
                }
            ],
            "Page": 1,
            "PageSize": 10
        }
        
        response = client.post("/query_knowledge_list", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "KnowledgeInfoList" in data

    def test_query_knowledge_list_pagination(self):
        """测试知识库列表分页"""
        headers = get_test_headers()
        params = {
            "Filters": [],
            "Sorts": [],
            "Page": 2,
            "PageSize": 5
        }
        
        response = client.post("/query_knowledge_list", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "KnowledgeInfoList" in data
        assert "Total" in data

    def test_create_knowledge_task_basic(self):
        """测试基本的知识任务创建"""
        headers = get_test_headers()
        document = generate_test_document("test_file.txt", "TXT")
        chunk_config = generate_test_chunk_config()
        
        params = {
            "KnowledgeBaseId": "test_kb",
            "Documents": [document],
            "Config": chunk_config
        }
        
        response = client.post("/create_knowledge_task", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_create_knowledge_task_multiple_documents(self):
        """测试创建包含多个文档的知识任务"""
        headers = get_test_headers()
        documents = [
            generate_test_document("file1.txt", "TXT"),
            generate_test_document("file2.pdf", "PDF"),
            generate_test_document("file3.docx", "DOCX")
        ]
        chunk_config = generate_test_chunk_config()
        
        params = {
            "KnowledgeBaseId": "test_kb",
            "Documents": documents,
            "Config": chunk_config
        }
        
        response = client.post("/create_knowledge_task", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_create_knowledge_task_different_chunk_types(self):
        """测试不同分块类型的知识任务创建"""
        headers = get_test_headers()
        # 只测试后端实际支持的分块类型（如 0, 1）
        chunk_types = [0, 1]
        for chunk_type in chunk_types:
            document = generate_test_document(f"test_file_{chunk_type}.txt", "TXT")
            chunk_config = generate_test_chunk_config(chunk_type=chunk_type)
            params = {
                "KnowledgeBaseId": "test_kb",
                "Documents": [document],
                "Config": chunk_config
            }
            response = client.post("/create_knowledge_task", json=params, headers=headers)
            data = check_success_response(response)
            assert "Status" in data

    def test_update_knowledge_task(self):
        """测试知识任务更新"""
        headers = get_test_headers()
        # 先创建知识任务，拿到 FileId
        document = generate_test_document("update_file.txt", "TXT")
        chunk_config = generate_test_chunk_config()
        create_params = {
            "KnowledgeBaseId": "test_kb",
            "Documents": [document],
            "Config": chunk_config
        }
        create_resp = client.post("/create_knowledge_task", json=create_params, headers=headers)
        create_data = check_success_response(create_resp)
        file_id = document["FileId"]
        # 再测更新
        update_params = {
            "FileId": file_id,
            "Config": chunk_config
        }
        response = client.post("/update_knowledge_task", json=update_params, headers=headers)
        data = check_error_response(response,400,"PARAM_ERROR")
        assert "Status" in data

    def test_knowledge_chunk_preview(self):
        """测试知识分块预览"""
        headers = get_test_headers()
        chunk_config = generate_test_chunk_config()
        
        params = {
            "FileUrl": "https://data-agent-dev-1353879163.cos.ap-chongqing.myqcloud.com/test.txt",
            "Config": chunk_config
        }
        
        response = client.post("/knowledge_chunk_preview", json=params, headers=headers)
        
        # 预览应该返回流式响应
        assert response.status_code == 200
        content = response.text
        assert "event:" in content

    def test_query_chunk_list_basic(self):
        """测试基本的分块列表查询"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "Page": 1,
            "PageSize": 10
        }
        response = client.post("/query_chunk_list", json=params, headers=headers)
        data = check_success_response(response)
        assert "Chunks" in data
        assert "Total" in data

    def test_query_chunk_list_with_content_filter(self):
        """测试带内容过滤的分块列表查询"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "Content": "test content",
            "Page": 1,
            "PageSize": 10
        }
        response = client.post("/query_chunk_list", json=params, headers=headers)
        data = check_success_response(response)
        assert "Chunks" in data

    def test_modify_chunk(self):
        """测试分块修改"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "ChunkId": str(uuid.uuid4()),
            "Content": "Modified chunk content"
        }
        
        response = client.post("/modify_chunk", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_modify_chunk_empty_content(self):
        """测试分块修改为空内容（应该失败）"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "ChunkId": str(uuid.uuid4()),
            "Content": ""
        }
        
        response = client.post("/modify_chunk", json=params, headers=headers)
        
        # 空内容应该返回验证错误
        assert response.status_code == 200

    def test_delete_chunk(self):
        """测试分块删除"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "ChunkIds": [str(uuid.uuid4()), str(uuid.uuid4())]
        }
        
        response = client.post("/delete_chunk", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_delete_file(self):
        """测试文件删除"""
        headers = get_test_headers()
        params = {
            "FileIds": [str(uuid.uuid4()), str(uuid.uuid4())]
        }
        
        response = client.post("/delete_file", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_add_chunk_basic(self):
        """测试基本的分块添加"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "Content": "New chunk content"
        }
        
        response = client.post("/add_chunk", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_add_chunk_with_position(self):
        """测试带位置信息的分块添加"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "Content": "New chunk content",
            "BeforeChunkId": str(uuid.uuid4()),
            # "AfterChunkId": str(uuid.uuid4()),  # 不传 after，避免参数冲突
            "InsertPos": 5
        }
        response = client.post("/add_chunk", json=params, headers=headers)
        data = check_success_response(response)
        assert "Status" in data

    def test_add_chunk_empty_content(self):
        """测试添加空内容分块（应该失败）"""
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "Content": ""
        }
        
        response = client.post("/add_chunk", json=params, headers=headers)
        
        # 空内容应该返回验证错误
        assert response.status_code == 200

    def test_query_knowledge_config(self):
        """测试知识库配置查询"""
        headers = get_test_headers()
        params = {}  # 通常不需要参数
        
        response = client.post("/query_knowledge_config", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "SearchConfig" in data

    def test_modify_knowledge_config(self):
        """测试知识库配置修改"""
        headers = get_test_headers()
        params = {
            "SearchConfig": {
                "Type": 0,
                "Num": 1000,
                "EmbeddingWeight": 0.5,
                "Rerank": 1
            }
        }
        
        response = client.post("/modify_knowledge_config", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_query_semantic_list(self):
        """测试语义列表查询"""
        headers = get_test_headers()
        params = {
            "Page": 1,
            "PageSize": 10
        }
        
        response = client.post("/query_semantic_list", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "SemanticConfigList" in data
        assert "Total" in data

    def test_add_semantic(self):
        """测试语义添加"""
        headers = get_test_headers()
        params = {
            "Term": "test_term",
            "Definition": "This is a test term definition",
            "Synonyms": ["synonym1", "synonym2"],
            "Scope": ["*.*.*"]
        }
        
        response = client.post("/add_semantic", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_add_semantic_empty_term(self):
        """测试添加空术语（应该失败）"""
        headers = get_test_headers()
        params = {
            "Term": "",
            "Definition": "This is a test term definition"
        }
        
        response = client.post("/add_semantic", json=params, headers=headers)
        
        # 空术语应该返回验证错误
        assert response.status_code == 200

    def test_modify_semantic(self):
        """测试语义修改"""
        headers = get_test_headers()
        
        # 先通过query_semantic_list获取已存在的term_id
        query_params = {
            "Page": 1,
            "PageSize": 10
        }
        query_resp = client.post("/query_semantic_list", json=query_params, headers=headers)
        query_data = check_success_response(query_resp)
        
        # 如果列表为空，先添加一个语义
        if not query_data.get("SemanticConfigList"):
            add_params = {
                "Term": "test_term_to_modify",
                "Definition": "to be modified"
            }
            add_resp = client.post("/add_semantic", json=add_params, headers=headers)
            check_success_response(add_resp)
            
            # 再次查询获取term_id
            query_resp = client.post("/query_semantic_list", json=query_params, headers=headers)
            query_data = check_success_response(query_resp)
        
        # 获取第一个term_id进行修改
        term_id = query_data["SemanticConfigList"][0]["TermId"]
        
        # 测试修改
        params = {
            "TermId": term_id,
            "Term": "modified_term",
            "Definition": "Modified definition"
        }
        response = client.post("/modify_semantic", json=params, headers=headers)
        data = check_success_response(response)
        assert "Status" in data

    def test_delete_semantic(self):
        """测试语义删除"""
        headers = get_test_headers()
        params = {
            "TermIds": [str(uuid.uuid4()), str(uuid.uuid4())]
        }
        
        response = client.post("/delete_semantic", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_add_feedback(self):
        """测试反馈添加"""
        headers = get_test_headers()
        params = {
            "RecordId": str(uuid.uuid4()),
            "Feedback": 1  # 假设1表示正面反馈
        }
        
        response = client.post("/add_feedback", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_add_feedback_invalid_record(self):
        """测试无效记录的反馈添加"""
        headers = get_test_headers()
        params = {
            "RecordId": "invalid_record_id",
            "Feedback": 1
        }
        
        response = client.post("/add_feedback", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_knowledge_invalid_file_type(self):
        """测试无效文件类型的知识任务创建"""
        headers = get_test_headers()
        document = generate_test_document("test_file.xyz", "XYZ")  # 无效文件类型
        chunk_config = generate_test_chunk_config()
        
        params = {
            "KnowledgeBaseId": "test_kb",
            "Documents": [document],
            "Config": chunk_config
        }
        
        response = client.post("/create_knowledge_task", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_knowledge_large_file_size(self):
        """测试大文件的知识任务创建"""
        headers = get_test_headers()
        document = generate_test_document("large_file.txt", "TXT")
        document["FileSize"] = 100 * 1024 * 1024  # 100MB
        chunk_config = generate_test_chunk_config()
        
        params = {
            "KnowledgeBaseId": "test_kb",
            "Documents": [document],
            "Config": chunk_config
        }
        
        response = client.post("/create_knowledge_task", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_knowledge_special_characters_in_filename(self):
        """测试文件名包含特殊字符的知识任务创建"""
        headers = get_test_headers()
        document = generate_test_document("test file with spaces & special chars!.txt", "TXT")
        chunk_config = generate_test_chunk_config()
        
        params = {
            "KnowledgeBaseId": "test_kb",
            "Documents": [document],
            "Config": chunk_config
        }
        
        response = client.post("/create_knowledge_task", json=params, headers=headers)
        
        data = check_success_response(response)
        assert "Status" in data

    def test_update_file(self):
        headers = get_test_headers()
        params = {
            "FileId": str(uuid.uuid4()),
            "FileName": "test",
        }

        response = client.post("/update_file", json=params, headers=headers)

        data = check_success_response(response)
        assert "Status" in data