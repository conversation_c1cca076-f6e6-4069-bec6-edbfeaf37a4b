from datetime import datetime
from typing import List, Tu<PERSON>, Optional, Dict

from peewee import Expression

from common.database.database import MysqlPool
from common.logger.logger import logger
from common.share import context
from infra.domain.knowledge_list_entity import Knowledge
from infra.domain.knowledge_list_port import KnowledgeListPort


class KnowledgeListAdapter(KnowledgeListPort):
    """代理列表适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def get_knowledge_list(self, ctx: context.Context, filter_conditions: List[Expression],
                           sort_conditions: List[Expression],
                           offset: int, limit: int) -> Tuple[int, List[Knowledge]]:
        with self.persistence.pool_db:
            """获取知识列表"""
            model = self.persistence.get_model('knowledge_list')

            query = model.select()
            # 2. 应用过滤条件（调用 Filter.to_peewee_condition）
            query = query.where(model.app_id == ctx.app_id)
            if filter_conditions:
                query = query.where(*filter_conditions)  # 多个条件用 AND 组合

            # 4. 应用排序条件（调用 Sort.to_peewee_order）
            if sort_conditions:
                query = query.order_by(*sort_conditions)
            else:
                query = query.order_by(model.create_time.desc())
            # 3. 计算总条数
            total = query.count()

            # 5. 应用分页
            paginated_query = query.offset(offset).limit(limit)
            try:
                # 6. 转换为 Pydantic 模型
                data = [
                    Knowledge(
                        file_id=item.file_id,
                        file_name=item.file_name,
                        file_size=item.file_size,
                        file_url=item.file_url,
                        status=item.status,
                        knowledge_base_id=item.knowledge_base_id,
                        type=item.type,
                        chunk_config=item.chunk_config or "{}",  # 如果为 None 则设置为 "{}"
                        create_user=item.create_user,
                        create_time=item.create_time,
                        update_time=item.update_time,
                        source=item.source,
                        is_show_case=item.is_show_case
                    ) for item in paginated_query
                ]
                return total, data
            except Exception as e:
                logger.error(f"Error occurred: {e}")
                return 0, []

    def get_knowledge_by_file_id(self, file_id: str) -> Optional[Knowledge]:
        """根据 file_id 查询 Knowledge 记录

        Args:
            file_id: 文件ID

        Returns:
            Knowledge 对象，未找到时返回 None
        """
        model = self.persistence.get_model('knowledge_list')
        with self.persistence.pool_db:
            try:
                item = model.get(model.file_id == file_id)
                return Knowledge(
                    file_id=item.file_id,
                    file_name=item.file_name,
                    file_size=item.file_size,
                    file_url=item.file_url,
                    status=item.status,
                    knowledge_base_id=item.knowledge_base_id,
                    type=item.type,
                    chunk_config=item.chunk_config or "{}",  # 如果为 None 则设置为 "{}"
                    create_user=item.create_user,
                    create_time=item.create_time,
                    update_time=item.update_time,
                    source=item.source,
                    is_show_case=item.is_show_case
                )
            except model.DoesNotExist:
                return None
            except Exception as e:
                logger.error(f"查询记录失败: {e}")
                return None

    def get_filenames_by_ids(self, file_ids: List[str]) -> Dict[str, str]:
        model = self.persistence.get_model('knowledge_list')
        with self.persistence.pool_db:
            try:
                query_results = model.select(model.file_id, model.file_name).where(model.file_id.in_(file_ids)).dicts()
                return {item['file_id']: item['file_name'] for item in query_results}
            except model.DoesNotExist:
                return {}
            except Exception as e:
                logger.error(f"查询记录失败: {e}")
                return {}

    def create_knowledge_task(self, ctx: context.Context, knowledge_task: Knowledge) -> bool:
        """创建知识列表"""
        model = self.persistence.get_model('knowledge_list')
        with self.persistence.pool_db:
            try:
                model.create(
                    app_id=ctx.app_id,
                    file_id=knowledge_task.file_id,
                    file_name=knowledge_task.file_name,
                    file_size=knowledge_task.file_size,
                    file_url=knowledge_task.file_url,
                    status=knowledge_task.status,
                    type=knowledge_task.type,
                    source=knowledge_task.source,
                    is_show_case=knowledge_task.is_show_case,
                    knowledge_base_id=knowledge_task.knowledge_base_id,
                    create_user=knowledge_task.create_user,
                    create_time=knowledge_task.create_time,
                )
                return True
            except Exception as e:
                logger.error(f"Error occurred: {e}")
                return False

    def batch_create_knowledge_tasks(self, ctx: context.Context, knowledge_tasks: List[Knowledge]) -> bool:
        """
        批量创建知识任务

        Args:
            ctx: 上下文
            knowledge_tasks: 知识任务列表

        Returns:
            bool: 是否全部插入成功
        """
        model = self.persistence.get_model('knowledge_list')
        try:
            # 准备批量插入数据
            data = [{
                'app_id': ctx.app_id,
                'file_id': task.file_id,
                'file_name': task.file_name,
                'file_size': task.file_size,
                'file_url': task.file_url,
                'status': task.status,
                'type': task.type,
                'chunk_config': task.chunk_config,
                'source': task.source,
                'knowledge_base_id': task.knowledge_base_id,
                'is_show_case': task.is_show_case,
                'create_user': task.create_user,
                'create_time': task.create_time
            } for task in knowledge_tasks]

            # 执行批量插入
            model.insert_many(data).execute()
            return True
        except Exception as e:
            logger.error(f"批量创建知识任务失败: {e}")
            raise e

    def update_status(self, file_id: str, new_status: int) -> bool:
        """更新任务状态"""
        model = self.persistence.get_model('knowledge_list')
        try:
            updated = model.update(
                status=new_status,
                update_time=datetime.now()
            ).where(
                model.file_id == file_id
            ).execute()

            return updated > 0
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            return False

    def update_knowledge(self, file_id: str, file_name: str) -> bool:
        model = self.persistence.get_model('knowledge_list')
        with self.persistence.pool_db:
            try:
                updated = model.update(
                    file_name=file_name
                ).where(
                    model.file_id == file_id
                ).execute()

                return updated > 0
            except Exception as e:
                logger.error(f"更新文件失败: {e}")
                return False

    def delete_knowledge_by_file_ids(self, file_ids: List[str]) -> bool:
        """根据多个 file_id 批量删除记录

        Args:
            file_ids: 文件ID列表

        Returns:
            是否成功执行（无异常即成功）
        """
        if not file_ids:
            return False

        with self.persistence.pool_db:
            model = self.persistence.get_model('knowledge_list')
            try:
                # 分批处理避免超长SQL
                batch_size = 1000
                for i in range(0, len(file_ids), batch_size):
                    batch = file_ids[i:i + batch_size]
                    # 执行删除，不检查实际删除条数
                    model.delete().where(model.file_id.in_(batch)).execute()
                return True  # 全部批次执行成功
            except Exception as e:
                logger.error(f"批量删除记录失败: {e}")
                return False


    def update_config(self, file_id: str, new_status: int, chunk_config: str) -> bool:
        model = self.persistence.get_model('knowledge_list')
        try:
            updated = model.update(
                status=new_status,
                chunk_config=chunk_config,
                update_time=datetime.now()
            ).where(
                model.file_id == file_id
            ).execute()

            return updated > 0
        except Exception as e:
            logger.error(f"更新任务配置失败: {e}")
            return False
