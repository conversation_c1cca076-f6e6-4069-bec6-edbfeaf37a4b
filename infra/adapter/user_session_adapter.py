from typing import Optional, List
from common.share import context
from peewee import DoesNotExist
from common.database.database import MysqlPool
from infra.domain.user_session_entity import UserSession
from common.logger.logger import logger
from datetime import datetime, timedelta


class UserSessionAdapter:
    """用户会话适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def create(self, ctx: context.Context, entity: UserSession) -> bool:
        """创建用户会话"""
        model = self.persistence.models['user_session']
        with self.persistence.pool_db:
            try:
                model.create(
                    sub_account_uin=entity.sub_account_uin,
                    session_id=entity.session_id,
                    session_title=entity.session_title,
                    db_info=entity.db_info,
                    run_record=entity.run_record,
                    create_at=datetime.now(),
                    updated_at=datetime.now(),
                    deleted_at=None
                )
                return True
            except Exception as e:
                logger.error(f"创建用户会话失败: {e}")
                return False

    def get_by_id(self, ctx: context.Context) -> Optional[UserSession]:
        """根据用户ID和会话ID获取会话信息"""
        model = self.persistence.models['user_session']
        three_months_ago = datetime.now() - timedelta(days=90)  # 计算三个月前的日期
        sub_account_uin = ctx.sub_account_uin
        session_id = ctx.session_id
        with self.persistence.pool_db:
            try:
                record = model.select().where(
                    (model.sub_account_uin == sub_account_uin) &
                    (model.session_id == session_id) &
                    (model.deleted_at.is_null()) &
                    (model.updated_at >= three_months_ago)  # 增加三个月以内的过滤条件
                ).get()

                return UserSession(
                    sub_account_uin=record.sub_account_uin,
                    session_id=record.session_id,
                    session_title=record.session_title,
                    db_info=record.db_info,
                    run_record=record.run_record,
                    created_at=record.created_at,
                    updated_at=record.updated_at
                )
            except DoesNotExist:
                return None

    def get_all_by_user(self, ctx: context.Context) -> List[UserSession]:
        """获取用户近三个月内的所有会话"""
        model = self.persistence.models['user_session']
        results = []
        sub_account_uin = ctx.sub_account_uin
        with self.persistence.pool_db:
            three_months_ago = datetime.now() - timedelta(days=90)  # 计算三个月前的日期
            logger.info(f"three_months_ago: {three_months_ago}")
            query = model.select().where(
                (model.sub_account_uin == sub_account_uin) &
                (model.deleted_at.is_null()) &
                (model.updated_at >= three_months_ago)
            ).order_by(model.updated_at.desc())
            for record in query:
                results.append(UserSession(
                    sub_account_uin=record.sub_account_uin,
                    session_id=record.session_id,
                    session_title=record.session_title,
                    db_info=record.db_info,
                    run_record=record.run_record,
                    created_at=record.created_at,
                    updated_at=record.updated_at
                ))
        return results

    def update_session(self, ctx: context.Context, run_record: str) -> bool:
        """更新会话"""
        model = self.persistence.models['user_session']
        with self.persistence.pool_db:
            try:
                update_fields = {
                    'run_record': run_record,
                }
                query = model.update(**update_fields).where(
                    (model.sub_account_uin == ctx.sub_account_uin) &
                    (model.session_id == ctx.session_id)
                )
                query.execute()
                return True
            except Exception as e:
                logger.error(f"更新会话: {e}")
                return False

    def update_session_info(self, ctx: context.Context, db_info: str) -> bool:
        """更新会话标题"""
        model = self.persistence.models['user_session']
        with self.persistence.pool_db:
            try:
                update_fields = {
                    'updated_at': datetime.now(),
                    'run_record': None,
                }
                if db_info is not None and db_info != "{}":
                    update_fields['db_info'] = db_info

                query = model.update(**update_fields).where(
                    (model.sub_account_uin == ctx.sub_account_uin) &
                    (model.session_id == ctx.session_id)
                )
                query.execute()
                return True
            except Exception as e:
                logger.error(f"更新会话标题失败: {e}")
                return False

    def mark_as_deleted(self, ctx: context.Context) -> bool:
        """标记会话为已删除"""
        model = self.persistence.models['user_session']
        sub_account_uin = ctx.sub_account_uin
        session_id = ctx.session_id
        with self.persistence.pool_db:
            try:
                query = model.update(
                    deleted_at=datetime.now(),
                    updated_at=datetime.now()
                ).where(
                    (model.sub_account_uin == sub_account_uin) &
                    (model.session_id == session_id) &
                    (model.deleted_at.is_null())  # 只更新未删除的记录
                )
                query.execute()
                return True
            except Exception as e:
                logger.error(f"标记会话为已删除失败: {e}")
                return False
