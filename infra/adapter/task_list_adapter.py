from datetime import datetime, timedelta, timezone
from typing import Optional, List

from peewee import DoesNotExist

from common.database.database import MysqlPool
from common.logger.logger import logger
from common.metric.enpoints import record_latency
from infra.domain.task_list_entity import TaskList


class TaskListAdapter:

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def create(self, entity: TaskList) -> bool:
        model = self.persistence.models['task_list']
        try:
            model.create(
                task_id=entity.task_id,
                file_id=entity.file_id,
                app_id=entity.app_id,
                file_url=entity.file_url,
                task_type=entity.task_type,
                knowledge_base_id=entity.knowledge_base_id,
                status=entity.status,
                task_params=entity.task_params,
                task_result=entity.task_result,
                create_time=entity.create_time,
            )
            return True
        except Exception as e:
            logger.error(f"创建task失败: {e}")
            return False

    def batch_create(self, entities: List[TaskList]) -> bool:
        """
        批量创建任务

        Args:
            entities: 任务实体列表

        Returns:
            bool: 是否全部插入成功
        """
        model = self.persistence.models['task_list']
        try:
            # 准备批量插入数据
            data = [{
                'task_id': entity.task_id,
                'app_id': entity.app_id,
                'file_id': entity.file_id,
                'file_url': entity.file_url,
                'task_type': entity.task_type,
                'knowledge_base_id': entity.knowledge_base_id,
                'status': entity.status,
                'task_params': entity.task_params,
                'task_result': entity.task_result,
                'create_time': entity.create_time
            } for entity in entities]

            # 执行批量插入
            model.insert_many(data).execute()
            return True
        except Exception as e:
            logger.error(f"批量创建任务失败: {e}")
            raise

    def get_by_id(self, task_id: str) -> Optional[TaskList]:
        model = self.persistence.models['task_list']
        with self.persistence.pool_db:
            try:
                record = model.select().where((model.task_id == task_id)).get()

                return TaskList(
                    task_id=record.task_id,
                    file_id=record.file_id,
                    file_url=record.file_url,
                    task_type=record.task_type,
                    knowledge_base_id=record.knowledge_base_id,
                    status=record.status,
                    task_params=record.task_params,
                    task_result=record.task_result,
                    create_time=record.create_time,
                    update_time=record.update_time,
                    node_id=record.node_id  # 新增字段
                )
            except DoesNotExist:
                return None

    def update_response(self, task_id: str, task_result: str) -> bool:
        model = self.persistence.models['task_list']
        with self.persistence.pool_db:
            try:
                query = model.update(
                    update_time=datetime.now(),
                    task_result=task_result
                ).where(model.task_id == task_id)
                query.execute()
                return True
            except Exception as e:
                logger.error(f"更新task失败: {e}")
                return False

    def get_task_list(self, limit: int) -> List[TaskList]:
        model = self.persistence.models['task_list']
        with self.persistence.pool_db:
            records = (model.select().
                       where(model.status == 0).
                       order_by(model.create_time.asc())
                       .limit(limit))
            try:
                # 转换为 Pydantic 模型
                data = [
                    TaskList(
                        task_id=record.task_id,
                        file_id=record.file_id,
                        app_id=record.app_id,
                        file_url=record.file_url,
                        task_type=record.task_type,
                        knowledge_base_id=record.knowledge_base_id,
                        status=record.status,
                        task_params=record.task_params,
                        task_result=record.task_result,
                        create_time=record.create_time,
                        update_time=record.update_time,
                    ) for record in records
                ]
                return data
            except Exception as e:
                logger.error(f"Error occurred: {e}")
                raise e

    def mark_task_running(self, limit: int, actor_name: str) -> List[TaskList]:
        try:
            model = self.persistence.models['task_list']
            with self.persistence.pool_db.atomic():
                records = (model.select().
                           where(model.status == 0).
                           order_by(model.create_time.asc())
                           .limit(limit).for_update(nowait=True))
                task_ids = [record.task_id for record in records]

                if task_ids:
                    # 更新状态为1（运行中）并记录更新时间
                    (model.update({
                        model.status: 1,
                        model.node_id: actor_name,
                        model.update_time: datetime.now()  # 确保导入datetime模块
                    }).where(model.task_id.in_(task_ids))).execute()
                    for record in records:
                        duration = datetime.now() - record.create_time
                        record_latency("rag_wait_task", duration.seconds)
                    data = [
                        TaskList(
                            task_id=record.task_id,
                            file_id=record.file_id,
                            app_id=record.app_id,
                            file_url=record.file_url,
                            task_type=record.task_type,
                            knowledge_base_id=record.knowledge_base_id,
                            status=1,  # 直接使用更新后的值，避免数据库读取延迟问题
                            task_params=record.task_params,
                            task_result=record.task_result,
                            create_time=record.create_time,
                            update_time=record.update_time,
                        ) for record in records
                    ]
                    return data
            return []
        except Exception as e:
            logger.warn(f"Error occurred: {e}")
            raise e
        finally:
            self.persistence.pool_db.manual_close()

    def mark_task_status(self, task_id, status: int, node_id: Optional[str] = None) -> bool:
        """
        标记任务状态，可选关联执行节点

        Args:
            task_id: 任务ID
            status: 任务状态
            node_id: 执行节点ID，可选
        """
        model = self.persistence.models['task_list']
        try:
            update_data = {
                'status': status,
                'update_time': datetime.now()
            }

            if node_id is not None:
                update_data['node_id'] = node_id

            query = model.update(update_data).where(model.task_id == task_id)
            query.execute()
            return True
        except Exception as e:
            logger.error(f"更新task失败: {e}")
            return False

    def mark_task_error(self, task_id, status: int, error_msg: str) -> bool:
        model = self.persistence.models['task_list']
        try:
            query = model.update(
                status=status,
                error_msg=error_msg,
                update_time=datetime.now()
            ).where(model.task_id == task_id)
            query.execute()
            return True
        except Exception as e:
            logger.error(f"更新task失败: {e}")
            return False

    def reset_tasks(self, actor_name: str) -> int:
        task_list_model = self.persistence.models['task_list']
        with self.persistence.pool_db:
            try:
                # 更新orphaned任务
                query = (task_list_model
                .update({
                    task_list_model.status: 0,  # 重置为待处理
                    task_list_model.node_id: None,
                    task_list_model.update_time: datetime.now()
                })
                .where(
                    (task_list_model.status == 1) &  # 只处理运行中的任务
                    (task_list_model.node_id == actor_name)
                ))
                updated_count = query.execute()
                logger.info(f"重置了 {updated_count} 个任务")
                return updated_count

            except Exception as e:
                logger.error(f"重置orphaned任务失败: {e}")
                return 0

    def reset_orphaned_tasks(self, heartbeat_timeout: int) -> int:
        """
        重置orphaned任务（关联到不活跃节点的运行中任务）

        Args:
            heartbeat_timeout: 心跳超时时间（秒）

        Returns:
            被重置的任务数量
        """
        task_list_model = self.persistence.models['task_list']
        task_node_model = self.persistence.models['task_nodes']
        with self.persistence.pool_db:
            try:
                # 计算超时时间点
                timeout_threshold = datetime.now() - timedelta(seconds=heartbeat_timeout)

                # 构建子查询，查找不活跃的节点ID
                inactive_nodes = (task_node_model
                .select(task_node_model.node_id)
                .where(
                    (task_node_model.last_heartbeat < timeout_threshold) |
                    (task_node_model.is_active == 0)
                ))

                # 更新orphaned任务
                query = (task_list_model
                .update({
                    task_list_model.status: 0,  # 重置为待处理
                    task_list_model.node_id: None,
                    task_list_model.update_time: datetime.now()
                })
                .where(
                    (task_list_model.status == 1) &  # 只处理运行中的任务
                    (task_list_model.node_id.in_(inactive_nodes) | task_list_model.node_id.is_null())
                ))

                return query.execute()

            except Exception as e:
                logger.error(f"重置orphaned任务失败: {e}")
                return 0

    def mark_task_time_out(self, limit: int, time_out: int) -> List[str]:
        try:
            model = self.persistence.models['task_list']
            knowledge_model = self.persistence.models['knowledge_list']
            start_time = datetime.now() - timedelta(seconds=time_out)
            with self.persistence.pool_db.atomic():
                records = (model.select().
                           where((model.status == 1) & (model.update_time <= start_time)).
                           order_by(model.create_time.asc())
                           .limit(limit).for_update(nowait=True))
                task_ids = [record.task_id for record in records]
                file_ids = [record.file_id for record in records]
                if task_ids:
                    # 更新状态为1（运行中）并记录更新时间
                    (model.update({
                        model.status: -1,
                        model.update_time: datetime.now()
                    }).where(model.task_id.in_(task_ids))).execute()
                    # 更新状态为1（运行中）并记录更新时间
                    (knowledge_model.update({
                        knowledge_model.status: -1,
                        knowledge_model.update_time: datetime.now()
                    }).where(knowledge_model.file_id.in_(file_ids))).execute()

                    logger.info(f"已超时处理 {task_ids} 任务")
                    return task_ids
            return []
        except Exception as e:
            logger.error(f"Error occurred: {e}")
            raise e
        finally:
            self.persistence.pool_db.manual_close()
