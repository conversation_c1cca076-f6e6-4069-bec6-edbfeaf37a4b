import os
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
from infra.mcp.aisearch import core
from common.logger.logger import logger
from typing import Any, Dict, List

mcp = FastMCP("AI Search Server")


@mcp.tool()
async def aisearch_retrieve(question: str) -> Dict[str, Any]:
    """知识库检索工具：基于用户查询进行智能文档检索

    参数:
        question (str): 用户的自然语言查询问题

    返回:
        Dictionary with these possible structures:

        Success case:
            {
                "answer": [{
                    "content": "",  # 检索到的文档结果
                    "score": 5, # 文档相关性评分
                    "rerank_score": 0.1, # rerank 模型重排后的评分
                }]
            }

        Error cases:
            {
                "error": str,  # 错误信息
                "details": str  # 详细错误描述
            }

    """
    try:
        params = {
            "question": question,
            "app_id": os.getenv('APP_ID', '')
        }
        # result = core.retrieve(params)
        result = core.retrieve_by_score(params)
        final_result = {
            "aisearch": result["answer"],
        }
        logger.info(f"aisearch core.retrieve app_id: {params['app_id']} question:{params['question']} final_result: {final_result}")
        return final_result
    except Exception as e:
        logger.error(f"aisearch core.retrieve error: {str(e)}", exc_info=True)
        return [{
            "error": "AI搜索执行失败",
            "details": str(e)
        }]


if __name__ == "__main__":
    mcp.run(transport="stdio") 