import os
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
from common.logger.logger import logger
from common.share.config import appConfig

from infra.mcp.nl2sql import generate
import json
mcp = FastMCP("Generate Sql Server")


@mcp.tool()
async def generate_sql(engine_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """根据自然语言生成SQL语句

    参数:
        engine_type (str): 默认值为 dlc；当分析日志时，使用 es_sql
        params (Dict[str, Any]): 包含以下键的字典参数:
            - question: 自然语言问题

    返回:
        Dict[str, Any]: 包含以下键的结果字典:
            - sql: 生成的SQL语句
            - reasoning: 推理过程说明
            - tables: 涉及的表列表
    """

    params['sub_account_uin'] = os.getenv('SUB_ACCOUNT_UIN', '')
    params['app_id'] = os.getenv('APP_ID', '')
    params['trace_id'] = os.getenv('TRACE_ID', '')
    params['data_engine_name'] = os.getenv('DATA_ENGINE_NAME', '')
    params['db_info'] = os.getenv('DB_TABLE', '')
    params['is_sampling'] = os.getenv('IS_SAMPLING', "IS_SAMPLING") == "True"
    params['mcp_url'] = json.loads(os.getenv('MCP_URL', '{}'))
    params['type'] = os.getenv('TYPE', '')
    params['record_id'] = os.getenv('RECORD_ID', '')

    # if engine_type == 'es_sql':
    #     # TODO for DNAN Demo
    #     mcp_url = {}
    #     mcp_list = appConfig.automic.mcp.example.get("data",{}).get("MCP",[])
    #     for mcp_tool in mcp_list:
    #         if mcp_tool["Type"] == "es_sql":
    #             mcp_url[mcp_tool["Type"]] = mcp_tool["Url"]
    #             params['mcp_url'] = mcp_url
    #             params['engine_type'] = "es_sql"
    #             params['dbname'] = mcp_tool.get("DBName", "")
    #             params['tables'] = mcp_tool.get("TableList", [])
    #             logger.info(f"register_default_servers config use es_sql engine_type: {params} mcp_url: {mcp_url}")
    
    try:
        logger.info(f"generate.generate_sql params: {params}")
        result = generate.generate_sql(params)
        logger.info(f"generate.generate_sql result: {result}")
    except Exception as e:
        logger.error(f"generate.generate_sql error: {str(e)}", exc_info=True)
        return {
            "error": "SQL generation failed.",
            "details": str(e)
        }
    return result

if __name__ == "__main__":
    mcp.run(transport="stdio")
