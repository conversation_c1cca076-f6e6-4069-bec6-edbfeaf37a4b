import json
from typing import List, Any

from mcp import types
from pydantic import BaseModel

from common.logger.logger import logger
from infra.mcp.nl2sql.mcp_client.client import call_mcp_tool
from infra.mcp.nl2sql.mcp_client.common_entity import Table, Column


class GetTCHouseDGetTablesSchemaReq(BaseModel):
    Catalog: str
    Database: str
    Tables: List[str]


class TCHouseDTableSchemas(BaseModel):
    Table: str
    CreateSql: str


class GetTCHouseDGetTablesSchemaRsp(BaseModel):
    RequestId: str
    Catalog: str
    Database: str
    Tables: List[TCHouseDTableSchemas]
    NotExistTable: list[str]

    @classmethod
    def default(cls) -> "GetTCHouseDGetTablesSchemaRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Catalog="",
            Database="",
            Tables=[],
            NotExistTable=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "GetTCHouseDGetTablesSchemaRsp":
        if not raw_result or not raw_result[0]:
            logger.error("查询数据库 schema 获取 mcp text content 内容为空")
            return GetTCHouseDGetTablesSchemaRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return GetTCHouseDGetTablesSchemaRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            tables_ddl_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house d 查询数据库 schema JSON解析失败,text: {content["text"]},error:{e}")
            return GetTCHouseDGetTablesSchemaRsp.default()
        if tables_ddl_json is None:
            logger.error("查询数据库 schema 获取 mcp text 内容为空")
            return GetTCHouseDGetTablesSchemaRsp.default()
        return cls.model_validate({
            "RequestId": tables_ddl_json.get("RequestId") or "",
            "Catalog": tables_ddl_json.get("Catalog") or "",
            "Database": tables_ddl_json.get("Database") or "",
            "Tables": [  # 结构化结果字段元数据
                TCHouseDTableSchemas(
                    Table=ddl.get("Table") or "",
                    CreateSql=ddl.get("CreateSql") or ""
                ) for ddl in tables_ddl_json.get("TableSchemas") or []
            ],
            "NotExistTable": tables_ddl_json.get("NotExistTable") or [],
        })


class TCHouseDSampleDataReq(BaseModel):
    Catalog: str
    Database: str
    Table: str
    Limit: int


class TCHouseDListTablesReq(BaseModel):
    Catalog: str
    Database: str
    Tables: List[str]


class TCHouseDSampleData(BaseModel):
    Table: str
    CreateSql: str


class TCHouseDSampleDataRsp(BaseModel):
    RequestId: str
    Catalog: str
    Database: str
    Table: str
    ResultSchema: list[str]  # 字段
    ResultSet: list[list[Any]]  # 数据

    @classmethod
    def default(cls) -> "TCHouseDSampleDataRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Catalog="",
            Database="",
            Table="",
            ResultSchema=[],
            ResultSet=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "TCHouseDSampleDataRsp":
        if not raw_result or not raw_result[0]:
            logger.error("tc house d 采样数据 mcp text content 返回为空")
            return TCHouseDSampleDataRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return TCHouseDSampleDataRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            mcp_rsp_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house d 采样数据JSON解析失败,text: {content["text"]},error:{e}")
            return TCHouseDSampleDataRsp.default()
        if mcp_rsp_json is None:
            logger.error("tc house d 采样数据 mcp text 为空")
            return TCHouseDSampleDataRsp.default()
        sample_data = mcp_rsp_json.get("SampleData") or []
        if len(sample_data) == 0:
            logger.error(f"tc house d 采样数据为空,Catalog:{mcp_rsp_json.get("Catalog")},"
                         f"Database:{mcp_rsp_json.get("Database")},Table:{mcp_rsp_json.get("Table")},"
                         f"request_id:{mcp_rsp_json.get("RequestId")}")
            return TCHouseDSampleDataRsp.default()

        columns = list(sample_data[0].keys()) if sample_data else []
        # 按固定键顺序提取所有值
        values = [
            [item[key] for key in columns]  # 使用固定键顺序保证一致性
            for item in sample_data
        ]
        return cls.model_validate({
            "RequestId": mcp_rsp_json.get("RequestId") or "",
            "Catalog": mcp_rsp_json.get("Catalog") or "",
            "Database": mcp_rsp_json.get("Database") or "",
            "Table": mcp_rsp_json.get("Table") or "",
            "ResultSchema": columns,
            "ResultSet": values,
        })


class TCHouseDListTableRsp(BaseModel):
    RequestId: str
    Catalog: str
    Database: str
    Tables: List[Table]

    @classmethod
    def default(cls) -> "TCHouseDListTableRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Catalog="",
            Database="",
            Tables=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "TCHouseDListTableRsp":
        if not raw_result or not raw_result[0]:
            logger.error("tc house d 获取表详细信息 mcp text content 返回为空")
            return TCHouseDListTableRsp.default()
            # 解析第一段文本内容中的JSON响应
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return TCHouseDListTableRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            mcp_rsp_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house d 获取表详细信息JSON解析失败,text: {content["text"]},error:{e}")
            return TCHouseDListTableRsp.default()
        if mcp_rsp_json is None:
            logger.error("tc house d  获取表详细信息 mcp text 为空")
            return TCHouseDListTableRsp.default()
        table_details = mcp_rsp_json.get("TableDetails") or []

        if len(table_details) == 0:
            logger.error(f"tc house d 获取表详细信息为空,Catalog:{mcp_rsp_json.get("Catalog")},"
                         f"Database:{mcp_rsp_json.get("Database")},"
                         f"request_id:{mcp_rsp_json.get("RequestId")}")
            return TCHouseDListTableRsp.default()
        tables = []
        for table_detail in table_details:
            columns = []
            for column in table_detail.get("Columns", []):
                columns.append(Column(Name=column.get("Field"), Type=column.get("Type"), Comment=column.get("Comment")))
            tables.append(Table(Name=table_detail.get("TableName") or "",
                                TableComment=table_detail.get("BasicInfo", {}).get("TableComment") or "",
                                Columns=columns))

        return cls.model_validate({
            "RequestId": mcp_rsp_json.get("RequestId") or "",
            "Catalog": mcp_rsp_json.get("Catalog") or "",
            "Database": mcp_rsp_json.get("Database") or "",
            "Tables": tables,
        })


class TCHouseDListTableNamesReq(BaseModel):
    Catalog: str
    Database: str


class TCHouseDListTableNamesRsp(BaseModel):
    RequestId: str
    Catalog: str
    Database: str
    TableNames: List[str]

    @classmethod
    def default(cls) -> "TCHouseDListTableNamesRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Catalog="",
            Database="",
            TableNames=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "TCHouseDListTableNamesRsp":
        if not raw_result or not raw_result[0]:
            logger.error("tc house d 获取表名称信息 mcp text content 返回为空")
            return TCHouseDListTableNamesRsp.default()
            # 解析第一段文本内容中的JSON响应
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return TCHouseDListTableNamesRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            mcp_rsp_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house d 获取表名称信息JSON解析失败,text: {content["text"]},error:{e}")
            return TCHouseDListTableNamesRsp.default()
        if mcp_rsp_json is None:
            logger.error("tc house d 获取表名称信息 mcp text 为空")
            return TCHouseDListTableNamesRsp.default()
        table_names = mcp_rsp_json.get("TableNames") or []

        if len(table_names) == 0:
            logger.error(f"tc house d 获取表名称为空,Catalog:{mcp_rsp_json.get("Catalog")},"
                         f"Database:{mcp_rsp_json.get("Database")},"
                         f"request_id:{mcp_rsp_json.get("RequestId")}")
            return TCHouseDListTableNamesRsp.default()
        return cls.model_validate({
            "RequestId": mcp_rsp_json.get("RequestId") or "",
            "Catalog": mcp_rsp_json.get("Catalog") or "",
            "Database": mcp_rsp_json.get("Database") or "",
            "TableNames": table_names,
        })


def tc_house_d_get_tables_schema(url: str, arguments: GetTCHouseDGetTablesSchemaReq,
                                 tool_name: str = "TCHouseDGetTableSchema") -> "GetTCHouseDGetTablesSchemaRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-d get schema tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return GetTCHouseDGetTablesSchemaRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-d get schema tool mcp 工具，响应解析失败，原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e


def tc_house_d_sample_data(url: str, arguments: TCHouseDSampleDataReq,
                           tool_name: str = "TCHouseDGetTableSample") -> "TCHouseDSampleDataRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-d sample_data tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return TCHouseDSampleDataRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-d sample data tool mcp 工具，响应解析失败 原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e


def tc_house_d_list_tables(url: str, arguments: TCHouseDListTablesReq,
                           tool_name: str = "TCHouseDListTableInfos") -> "TCHouseDListTableRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-d list_tables tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return TCHouseDListTableRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-d list tables tool mcp 工具 响应解析失败，原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e


def tc_house_d_list_table_names(url: str, arguments: TCHouseDListTableNamesReq,
                                tool_name: str = "TCHouseDListTableNames") -> "TCHouseDListTableNamesRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-d list_table_names tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return TCHouseDListTableNamesRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-d list table names tool mcp 工具 响应解析失败，原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e
