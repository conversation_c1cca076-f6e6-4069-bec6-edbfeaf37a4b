from datetime import datetime, timezone
from typing import Dict, List, Optional

from pydantic import BaseModel, Field

from common.database.es_operator import ESOperator
from common.logger.logger import logger
from common.share import config
from common.share.config import ESConfig
from infra.mcp.nl2sql.llm.llm import embedding_texts


class Nl2sqlEsQueryParams(BaseModel):
    vector_field: str
    vector: list[float]
    app_id: str
    datasource_name: Optional[str] = None
    dbname: Optional[str] = None
    engine_type: str
    feedback: Optional[int] = None
    top_k: int
    min_score: Optional[float] = None
    script:Dict


class Nl2sqlSaveEsParam(BaseModel):
    app_id: Optional[str]  # 应用ID
    sub_account_uin: Optional[str]  # 用户ID
    trace_id: Optional[str]  # 追踪ID
    record_id: Optional[str]  # 记录唯一标识
    datasource_name: Optional[str]  # 数据源名称
    dbname: Optional[str]  # 数据库名称
    feedback: Optional[int] = None
    engine: Optional[str]  # 查询引擎类型
    engine_type: Optional[str]  # 引擎子类型
    question: str  # 自然语言问题
    sql: Optional[str]  # 生成的SQL语句
    embedding_model: str  # 使用的嵌入模型名称
    embedding_dims: int  # 嵌入模型维度
    db_schema: Optional[str] = None
    vector_question: Optional[List[float]]
    vector_sql: Optional[List[float]]
    create_time: datetime
    update_time: datetime
    tables:Optional[List[str]]

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class Nl2sqlRecordData(BaseModel):
    app_id: Optional[str]  # 应用ID
    sub_account_uin: Optional[str]  # 用户ID
    trace_id: Optional[str]  # 追踪ID
    record_id: Optional[str]  # 记录唯一标识
    datasource_name: Optional[str]  # 数据源名称
    dbname: Optional[str]  # 数据库名称
    feedback: Optional[int] = None
    engine: Optional[str]  # 查询引擎类型
    engine_type: Optional[str]  # 引擎子类型
    question: str  # 自然语言问题
    sql: Optional[str]  # 生成的SQL语句
    embedding_model: str  # 使用的嵌入模型名称
    embedding_dims: int  # 嵌入模型维度
    db_schema: Optional[str] = None
    score: float = 0.0
    tables: Optional[List[str]]


class UpdateNl2sqlRecordParam(BaseModel):
    trace_id: str  # 追踪ID
    record_id: str  # 记录唯一标识
    feedback: int = Field(ge=1, le=2)  # 用户反馈(1-肯定反馈, 2-否定反馈)


class Nl2SQLVectorStore(ESOperator):
    def __init__(self, conf: ESConfig, trace_id: str, embedding_dims: int,app_id:str):
        super().__init__(conf)
        self.trace_id = trace_id
        self.app_id = app_id
        self.embedding_dims = embedding_dims
        self.index_name = f"{self.config.index_name}_{self.app_id}".lower()
        self._ensure_index_exists()

    def _ensure_index_exists(self):
        mapping = {
            "settings": {
                "number_of_shards": 5,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {
                        "type": "keyword"
                    },
                    "sub_account_uin": {
                        "type": "keyword"
                    },
                    "trace_id": {
                        "type": "keyword"
                    },
                    "record_id": {
                        "type": "keyword"
                    },
                    "datasource_name": {
                        "type": "keyword"
                    },
                    "dbname": {
                        "type": "keyword"
                    },
                    "feedback": {
                        "type": "short"
                    },
                    "engine": {
                        "type": "keyword"
                    },
                    "engine_type": {
                        "type": "keyword"
                    },
                    "question": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "sql": {
                        "type": "keyword"
                    },
                    "embedding_model": {
                        "type": "keyword"
                    },
                    "embedding_dims": {
                        "type": "keyword"
                    },
                    "db_schema": {
                        "type": "keyword"
                    },
                    "vector_question": {
                        "type": "dense_vector",
                        "dims": self.embedding_dims
                    },
                    "vector_sql": {
                        "type": "dense_vector",
                        "dims": self.embedding_dims
                    },
                    "create_time": {
                        "type": "date"
                    },
                    "update_time": {
                        "type": "date"
                    },
                    "tables": {
                        "type": "keyword"
                    }
                }
            }
        }
        try:
            if not self.sync_client.indices.exists(index=self.index_name):
                self.sync_client.indices.create(
                    index=self.index_name,
                    body=mapping
                )
                logger.info(f"Create index {self.index_name} successfully,trace_id: {self.trace_id}")
        except Exception as e:
            logger.error(f"Create index {self.index_name} failed,trace_id: {self.trace_id},e:{e}")
            raise

    @ESOperator._retry_decorator
    def save_nl2sql_record(self, nl2sql_record: Nl2sqlSaveEsParam) -> str:
        param = nl2sql_record.model_dump()
        if len(nl2sql_record.vector_sql) == 0:
            param.pop("vector_sql")
        if len(nl2sql_record.vector_question) == 0:
            param.pop("vector_question")
        response = self.sync_client.index(
            index=self.index_name,
            document=param
        )
        return response["_id"]

    @ESOperator._retry_decorator
    def get_nl2sql_records_for_script(self, params: Nl2sqlEsQueryParams) -> list[Nl2sqlRecordData]:
        query = {
            "query": {
                "script_score": {
                    "query": {
                        "bool": {
                            "filter": [
                                {
                                    "exists": {
                                        "field": params.vector_field
                                    }
                                }
                            ],
                            "must": [
                                {"term": {"app_id": params.app_id}},
                                *([{"term": {
                                    "datasource_name": params.datasource_name}}] if params.datasource_name else []),
                                *([{"term": {"dbname": params.dbname}}] if params.dbname else []),
                                {"term": {"engine_type": params.engine_type}},
                                *([{"term": {"feedback": params.feedback}}] if params.feedback is not None else [])
                            ]
                        }
                    },
                    "script": params.script
                }
            },
            "size": params.top_k,
            "_source": True,  # 返回原始文档
            "track_scores": True,  # 跟踪分数计算
            "min_score": params.min_score if params.min_score is not None else 0.0  # 添加分数阈值过滤
        }
        response = self.sync_client.search(
            index=self.index_name,
            body=query
        )
        logger.info(f"get examples trace_id: {self.trace_id},script: {params.script},response: {response}")
        return [Nl2sqlRecordData(
            **hit["_source"],
            score=hit.get("_score", 0.0)  # 获取计算分数，默认0.0防止异常
        ) for hit in response["hits"]["hits"]]

    @ESOperator._retry_decorator
    def update_nl2sql_record(self, params: UpdateNl2sqlRecordParam) -> bool:
        update_body = {
            "query": {"term": {"record_id": params.record_id}},

            "script": {
                "lang": "painless",
                "params": {"new_feedback": params.feedback, "update_time": datetime.now(timezone.utc).isoformat()},
                "source": """
                          ctx._source.feedback = params.new_feedback;
                          ctx._source.update_time = params.update_time;
                        """
            }
        }

        logger.info(f"Start update nl2sql record feedback params：{params.model_dump()},trace_id: {params.trace_id}")
        try:
            response = self.sync_client.update_by_query(
                index=self.index_name,
                body=update_body,
                refresh=True
            )
            logger.info(f"Update nl2sql record feedback success：{response}")
            return True
        except Exception as e:
            logger.error(f"Update nl2sql record feedback,params={params.model_dump()},trace_id:{params.trace_id},"
                         f" exception：{e}")
            # 其他未知异常（如网络问题）
            return False


if __name__ == '__main__':
    # {
    #     "app_id": "1",
    #     "sub_account_uin": "collinsdeng",
    #     "trace_id": "collinsdeng",
    #     "engine": "data-agent-exp-dev",
    #     "datasource_name": "DataLakeCatalog",
    #     "dbname": "nl2sql_test",
    #     "tables": ["orders", "products", "customers"],
    #     "is_sampling": True,
    #     "mcp_url": {"dlc": "http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025"},
    #     "engine_type": "dlc",
    #     "question": "5月下过单的所有客户名称,以及邮箱是啥？",
    #     "record_id": "5",
    # }

    es_config = config.appConfig.automic.nl2sql.es
    vector_store = Nl2SQLVectorStore(es_config, "1", 1024,"1")
    update_nl2sql_record = vector_store.update_nl2sql_record(UpdateNl2sqlRecordParam(trace_id="1", record_id="5",
                                                                                     feedback=2))
    print(update_nl2sql_record)
    question_emb = embedding_texts(["所有订单状态为已发货的客户id，是啥？"])[0]
    script = {
        "source": "cosineSimilarity(params.query_vector, 'vector_question') + 1.0",
        "params": {
            "query_vector": question_emb
        }
    }
    p = Nl2sqlEsQueryParams(vector_field="vector_question", vector=question_emb, app_id="1",
                            datasource_name="DataLakeCatalog", dbname="nl2sql_test", top_k=10, min_score=0.8,
                            script=script)
    rsp = vector_store.get_nl2sql_records_for_script(p)
    print(rsp)
