import numpy as np
from typing import Dict, List, Tuple
from common.logger.logger import logger
import difflib
from infra.mcp.nl2sql.llm.llm import embedding_texts

from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.utils.db_util import Column


class EntityRetrieval:
    lsh: LSH
    keywords: List[str]
    db_schema: Dict[str, List[Column]]
    question: str

    def __init__(self, lsh: LSH, keywords: List[str], db_schema: Dict[str, List[Column]], question: str, trace_id=str):
        self.lsh = lsh
        self.keywords = keywords
        self.db_schema = db_schema
        self.question = question
        self.trace_id = trace_id

    def get_similar_columns(self) -> Dict[str, List[str]]:
        """
        基于问题和关键词检索数据库中相似的列名

        通过关键词匹配和语义相似度计算，从数据库模式中检索与问题相关的列名

        Returns:
            Dict[str, List[str]]: 表到列的映射字典，结构为:
                {
                    "表名1": ["相似列名1", "相似列名2"],
                    "表名2": ["相似列名3"]
                }
        """
        logger.info(f"Retrieving similar columns,trace_id:{self.trace_id},question:{self.question}")
        selected_columns = {}
        # 调用内部方法获取带相似度的列名列表（表名, 列名, 相似度）
        similar_columns = self._get_similar_column_names(potential_column_names=self.keywords, question=self.question,
                                                         db_schema=self.db_schema)

        # 将结果按表名分组整理为字典结构
        for table_name, column_name in similar_columns:
            # 使用setdefault确保表名键存在，并追加列名到列表
            selected_columns.setdefault(table_name, []).append(column_name)
        return selected_columns

    def get_similar_entities(self) -> Dict[str, Dict[str, List[str]]]:

        logger.info(f"Retrieving similar entities,trace_id:{self.trace_id},question:{self.question}")
        selected_values = {}

        def get_similar_values_target_string(target_string: str):
            """内部处理函数：针对单个关键词获取相似值"""
            # 使用LSH查询获取前10个相似值（包含相似度分数）
            unique_similar_values_with_sim = self.lsh.query_lsh(
                keyword=target_string, top_n=10
            )

            # 合并多个查询结果并去重
            unique_similar_values = merge_and_deduplicate(unique_similar_values_with_sim)

            return self._get_similar_entities_to_keyword(
                target_string, unique_similar_values
            )

        for ts in self.keywords:
            similar_values = get_similar_values_target_string(ts)
            for table_name, column_values in similar_values.items():
                for column_name, entities in column_values.items():
                    if entities:
                        # 使用setdefault确保嵌套字典结构存在
                        selected_values.setdefault(table_name, {}).setdefault(
                            column_name, []
                        ).extend(
                            [
                                (ts, value, edit_distance, embedding_value)
                                for ts, value, edit_distance, embedding_value in entities
                            ]
                        )
        # # 使用线程池并行处理所有关键词
        # with concurrent.futures.ThreadPoolExecutor() as executor:
        #     # 提交所有关键词查询任务
        #     futures = {
        #         executor.submit(get_similar_values_target_string, ts): ts for ts in self.keywords
        #     }
        #     # 按完成顺序处理结果
        #     for future in concurrent.futures.as_completed(futures):
        #         similar_values = future.result()
        #         # 将结果按表结构整理到selected_values字典
        #         for table_name, column_values in similar_values.items():
        #             for column_name, entities in column_values.items():
        #                 if entities:
        #                     # 使用setdefault确保嵌套字典结构存在
        #                     selected_values.setdefault(table_name, {}).setdefault(
        #                         column_name, []
        #                     ).extend(
        #                         [
        #                             (ts, value, edit_distance, embedding_value)
        #                             for ts, value, edit_distance, embedding_value in entities
        #                         ]
        #                     )

        # 后处理：筛选每个列的最大编辑距离值
        for table_name, column_values in selected_values.items():
            for column_name, values in column_values.items():
                # 找出当前列所有值中的最大编辑距离
                max_edit_distance = max(values, key=lambda x: x[2])[2]
                # 筛选出具有最大编辑距离的值，并进行去重
                selected_values[table_name][column_name] = list(
                    set(
                        value
                        for _, value, edit_distance, _ in values
                        if edit_distance == max_edit_distance  # 保留编辑距离最大的值
                    )
                )
        return selected_values

    # def entity_retrieval(
    #         question: str, keywords: List[str], db_schema: Dict[str, List[str]], lsh: LSH) -> Dict[str, Any]:
    #     """执行实体检索流程，获取与问题相关的数据库列和值
    #
    #     通过关键词匹配和语义相似度计算，从数据库模式中检索相关列和实体值
    #
    #     Args:
    #         question (str): 用户输入的原始问题文本
    #         keywords (List[str]): 从问题中提取的关键词列表
    #         db_schema (Dict[str, List[str]]): 数据库模式结构，格式为 {表名: [列名列表]}
    #         lsh (LSH): 局部敏感哈希对象，用于高效相似值检索
    #
    #     Returns:
    #         Dict[str, Any]: 包含检索结果的字典，结构为:
    #             {
    #                 "similar_columns": {表名: [相似列名列表]},  # 表到列的映射关系
    #                 "similar_values": {表名: {列名: [相似值列表]}}  # 表-列到值的映射关系
    #             }
    #     """
    #     logger.info("Starting entity retrieval")
    #
    #     # 通过关键词和问题文本获取相似列（包含表结构信息）
    #
    #     result = {"similar_columns": {}}
    #
    #     # 使用LSH索引快速检索相似实体值（基于哈希的近似最近邻搜索）
    #     if lsh is not None:
    #         similar_values = get_similar_entities(keywords=keywords, lsh=lsh)
    #         result["similar_values"] = similar_values
    #
    #     logger.info("Entity retrieval completed successfully")
    #     return result

    def _get_similar_column_names(self,
                                  potential_column_names: List[str],
                                  question: str,
                                  db_schema: Dict[str, List[Column]],
                                  threshold: float = 0.8,
                                  ) -> List[Tuple[str, str]]:
        """
        基于关键词匹配和语义相似度检索数据库中的相似列名

        通过以下步骤实现：
        1. 遍历数据库模式中的所有表和列
        2. 使用字符串相似度进行初步筛选
        3. 对匹配的列进行语义相似度计算
        4. 按相似度排序并返回最佳匹配

        Args:
            potential_column_names (List[str]): 候选列名列表（从问题中提取的关键词）
            question (str): 用户输入的原始问题文本
            db_schema (Dict[str, List[Column]]): 数据库模式字典，结构为 {表名: [列名列表]}
            threshold (float, optional): 字符串相似度阈值，默认0.8（0-1范围）

        Returns:
            List[Tuple[str, str]]: 排序后的表列名元组列表，每个元组格式为（表名，列名）
        """

        similar_column_names = []
        # 遍历数据库所有表和列
        for table, columns in db_schema.items():
            for column in columns:
                # 对每个候选列名进行匹配检查
                for potential_column_name in potential_column_names:
                    # 使用字符串相似度进行初步筛选
                    if _does_keyword_match_column(
                            potential_column_name, column.Name, threshold=threshold
                    ):
                        # 计算语义相似度（结合表结构信息）
                        similarity_score = self._get_semantic_similarity_with_openai(
                            f"`{table}`.`{column}`", [question]
                        )[0]
                        # 记录匹配结果及相似度分数
                        similar_column_names.append((table, column, similarity_score))

        # 按相似度分数降序排序
        similar_column_names.sort(key=lambda x: x[2], reverse=True)
        # 返回相似度最高的前1个结果（过滤掉分数）
        return [(table, column.Name) for table, column, _ in similar_column_names[:1]]

    def _get_similar_entities_to_keyword(self,
                                         keyword: str, unique_values: Dict[str, Dict[str, List[str]]]
                                         ) -> Dict[str, Dict[str, List[Tuple[str, str, float, float]]]]:
        """
        Finds entities similar to a keyword in the database.

        Args:
            keyword (str): The keyword to find similar entities for.
            unique_values (Dict[str, Dict[str, List[str]]]): The dictionary of unique values from the database.

        Returns:
            Dict[str, Dict[str, List[Tuple[str, str, float, float]]]]: A dictionary mapping table and column names to
            similar entities.
        """
        return {
            table_name: {
                column_name: self._get_similar_values(keyword, values)
                for column_name, values in column_values.items()
            }
            for table_name, column_values in unique_values.items()
        }

    def _get_similar_values(self,
                            target_string: str, values: List[str]
                            ) -> List[Tuple[str, str, float, float]]:
        """
        基于编辑距离和语义相似度检索与目标字符串相似的值（两阶段筛选）

        实现逻辑：
        1. 第一阶段：基于编辑距离的粗筛
        2. 第二阶段：基于语义嵌入的精确筛选
        3. 最终结果综合两种相似度进行排序

        Args:
            target_string (str): 需要匹配的目标字符串（如用户问题中的关键词）
            values (List[str]): 候选值列表（来自数据库的实体值）

        Returns:
            List[Tuple[str, str, float, float]]: 匹配结果列表，每个元组包含：
                - 目标字符串
                - 匹配值
                - 编辑距离相似度（0-1）
                - 语义嵌入相似度（0-1）
        """
        # 第一阶段筛选参数：编辑距离阈值和保留数量
        edit_distance_threshold = 0.3  # 最低相似度要求（排除明显不匹配的值）
        top_k_edit_distance = 5  # 保留编辑距离前5的候选值

        # 第二阶段筛选参数：语义相似度阈值和最终保留数量
        embedding_similarity_threshold = 0.6  # 语义相似度最低要求
        top_k_embedding = 1  # 最终返回最优的1个结果

        # 第一阶段：基于编辑距离的初步筛选
        edit_distance_similar_values = [
            (
                value,
                # 计算标准化编辑距离相似度（转换为0-1的比值）
                difflib.SequenceMatcher(None, value.lower(), target_string.lower()).ratio(),
            )
            for value in values
            # 过滤低于阈值的候选值（提升后续处理效率）
            if difflib.SequenceMatcher(None, value.lower(), target_string.lower()).ratio() >= edit_distance_threshold
        ]

        if len(edit_distance_similar_values) < 1:
            return []

        # 按相似度降序排序并截取前N个结果
        edit_distance_similar_values.sort(key=lambda x: x[1], reverse=True)
        edit_distance_similar_values = edit_distance_similar_values[:top_k_edit_distance]

        # 第二阶段：基于语义嵌入的精确匹配
        # 获取筛选后值的语义相似度（批量查询提升效率）
        similarities = self._get_semantic_similarity_with_openai(
            target_string, [value for value, _ in edit_distance_similar_values]
        )

        # 合并两种相似度结果并进行最终筛选
        embedding_similar_values = [
            (
                target_string,
                edit_distance_similar_values[i][0],  # 原始值
                edit_distance_similar_values[i][1],  # 编辑距离相似度
                similarities[i],  # 语义相似度
            )
            for i in range(len(edit_distance_similar_values))
            # 同时满足语义相似度阈值要求
            if similarities[i] >= embedding_similarity_threshold
        ]

        # 按编辑距离相似度二次排序（优先保证字符层面的相似性）
        embedding_similar_values.sort(key=lambda x: x[2], reverse=True)
        # 返回最终筛选结果（综合两种相似度的最优解）
        return embedding_similar_values[:top_k_embedding]

    def _get_semantic_similarity_with_openai(self,
                                             target_string: str, list_of_similar_words: list[str]
                                             ) -> List[float]:
        """
        使用OpenAI嵌入计算目标字符串与相似词列表的语义相似度

        实现步骤：
        1. 获取目标字符串的嵌入向量
        2. 批量获取所有相似词的嵌入向量
        3. 计算余弦相似度（通过点积实现，因为嵌入已归一化）

        Args:
            target_string (str): 需要比较的目标字符串（例如数据库列名或实体值）
            list_of_similar_words (List[str]): 待比较的相似词列表（例如用户问题中的关键词）

        Returns:
            List[float]: 相似度分数列表，数值范围[0,1]，索引顺序与输入list_of_similar_words一致
        """
        # 获取目标字符串的嵌入向量（单个查询专用接口）
        target_string_embedding = embedding_texts([target_string])
        if not target_string_embedding:
            logger.error(
                f"目标字符串嵌入失败,trace_id:{self.trace_id},question:{self.question}，内容:'{target_string}',返回空相似度列表")
            return [0.0] * len(list_of_similar_words)  # 返回与输入长度一致地默认相似度

        # 批量获取相似词嵌入（增加空数组保护）
        all_embeddings = embedding_texts(list_of_similar_words)
        if not all_embeddings:
            logger.error(f"相似词嵌入批量获取失败,trace_id:{self.trace_id},question:{self.question},目标字符串:'{target_string}'")
            return [0.0] * len(list_of_similar_words)

        # 安全计算相似度（处理空数组和维度匹配）
        try:
            similarities = [
                np.dot(target_string_embedding[0], embedding_value)
                for embedding_value in all_embeddings
                if embedding_value is not None  # 过滤无效嵌入
            ]
            # 对齐结果长度（对无效嵌入返回0相似度）
            return similarities + [0.0] * (len(list_of_similar_words) - len(similarities))
        except IndexError as e:
            logger.error(f"向量维度不匹配,trace_id:{self.trace_id},question:{self.question},"
                         f"目标字符串:'{target_string[:50]},error:{e}'")
            return [0.0] * len(list_of_similar_words)


def _does_keyword_match_column(keyword: str, column_name: str, threshold: float = 0.9
                               ) -> bool:
    """
    检查关键词与列名的相似度是否达到阈值（基于字符串预处理和序列匹配）

    实现逻辑：
    1. 对关键词和列名进行标准化处理（统一小写、移除空格和下划线、去除复数形式）
    2. 使用diff-lib计算标准化后的字符串相似度
    3. 判断相似度是否达到预设阈值

    Args:
        keyword (str): 需要匹配的关键词（通常来自用户问题）
        column_name (str): 数据库中的列名（来自数据库模式）
        threshold (float, optional): 相似度阈值，范围0-1。默认0.9表示严格匹配

    Returns:
        bool: 当相似度≥阈值时返回True，否则返回False
    """
    # 标准化处理：统一小写，移除空格和下划线，处理复数形式（去除末尾's'）
    keyword_normalizing = keyword.lower().replace(" ", "").replace("_", "").rstrip("s")
    column_name = column_name.lower().replace(" ", "").replace("_", "").rstrip("s")

    # 使用序列匹配算法计算相似度（基于最长公共子序列）
    similarity = difflib.SequenceMatcher(None, column_name, keyword_normalizing).ratio()

    return similarity >= threshold


def merge_and_deduplicate(data: List[Tuple[Dict[str, Dict[str, List[str]]], float]]) -> Dict[str, Dict[str, List[str]]]:
    """合并多个嵌套字典并去重，保持元素顺序

    处理流程：
    1. 合并阶段：将多个嵌套字典的内容合并到统一结构中
    2. 去重阶段：对每个列表元素进行顺序保持的去重

    Args:
        data (List[Tuple[Dict, float]]): 输入数据列表，每个元素为元组：
            - 嵌套字典结构：{表名: {列名: [值列表]}
            - float: 关联的权重分数（本函数未实际使用）

    Returns:
        Dict[str, Dict[str, List[str]]]: 合并去重后的嵌套字典结构
    """
    merged_dict: Dict[str, Dict[str, List[str]]] = {}

    # 第一步：合并字典
    # 遍历每个数据项的嵌套字典结构（忽略权重分数）
    for nested_dict, _ in data:
        # 外层遍历：表级别处理
        for outer_key, inner_dict in nested_dict.items():
            # 初始化表结构（如果不存在）
            if outer_key not in merged_dict:
                merged_dict[outer_key] = {}
            # 内层遍历：列级别处理
            for inner_key, values in inner_dict.items():
                # 初始化列结构（如果不存在）
                if inner_key not in merged_dict[outer_key]:
                    merged_dict[outer_key][inner_key] = []
                # 扩展值列表
                merged_dict[outer_key][inner_key].extend(values)

    # 第二步：去重列表（保持顺序）
    # 遍历所有表结构
    for outer_key in merged_dict:
        # 遍历表内的所有列
        for inner_key in merged_dict[outer_key]:
            # 使用字典键的唯一性去重（Python 3.7+ 保证顺序）
            # 原理：dict.fromkeys()会按插入顺序保留第一个出现的元素
            merged_list = merged_dict[outer_key][inner_key]
            merged_dict[outer_key][inner_key] = list(dict.fromkeys(merged_list))

    return merged_dict
