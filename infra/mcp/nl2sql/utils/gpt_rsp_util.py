import json
import re
from typing import Dict, Any
from common.logger.logger import logger


def parse_last_python_code(output_str):
    """从字符串中解析最后一个Python代码块并进行格式化处理

    参数:
        output_str (str): 包含可能多个代码块的原始字符串

    返回:
        str: 处理后的单行代码字符串（若找到代码块则返回最后一个，否则返回原字符串）
    """
    # 使用正则表达式匹配最后一个```python代码块
    # (?!.*```python) 使用负向先行断言确保后面没有其他python代码块标记
    # re.DOTALL 使.可以匹配换行符
    pat = re.compile(r"```python(?!.*```python)(.*?)```", re.DOTALL)
    code = re.findall(pat, output_str)

    if code:
        code = code[0]
        # 将代码块中的换行符替换为空格
        if "\n" in code:
            code = re.sub(r"\n", " ", code)
        # 压缩多个连续空白字符为单个空格
        code = re.sub(r"\s+", " ", code)
    else:
        # 未找到代码块时返回原始字符串
        code = output_str
    return code


def extract_first_json_block(md_content: str) -> Dict[str, Any]:
    """
    从 Markdown 内容中提取并解析第一个 JSON 代码块

    参数:
        md_content (str): 包含 JSON 代码块的 Markdown 内容

    返回:
        Dict: 解析后的第一个有效 JSON 对象

    异常:
        ValueError: 如果未找到有效 JSON 代码块或解析失败
    """
    # 匹配第一个 JSON 代码块（优先匹配带 ```json 标识的块）
    code_block_pattern = r'''
        (?:^```json\s*?\n(.*?)```\s*$)  # 带 json 标识的代码块
        |
        (?:^```\s*?\n(.*?)```\s*$)      # 普通代码块（第二优先级）
    '''

    # 查找第一个匹配的代码块
    match = re.search(code_block_pattern, md_content,
                      flags=re.MULTILINE | re.DOTALL | re.VERBOSE)


    try:
        if not match:
            logger.error(f"Failed to parse JSON code returned from GPT,no code blocks detected, "
                         f"content={md_content}")
            return json.loads(md_content)

        # 优先取带 json 标识的块，否则取普通代码块
        json_str = match.group(1) or match.group(2)

        if not json_str:
            logger.error(f"Failed to parse JSON code returned from GPT,no code blocks detected,{json_str} ")
            return {}

        # 预处理步骤（处理常见问题）
        cleaned = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)  # 移除注释
        cleaned = re.sub(r',\s*(?=[}\]])', '', cleaned)  # 修复尾部逗号
        cleaned = re.sub(r'\t', '  ', cleaned)  # 替换制表符为空格

        return json.loads(cleaned)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON code returned from GPT, content={md_content},error={e}")
        return {}
