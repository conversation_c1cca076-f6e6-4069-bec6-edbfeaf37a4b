import json
from infra.mcp.nl2sql.entities.engine_type_enum import EngineTypeEnum
import logging

logger = logging.getLogger(__name__)


def validate_common_params(params):
    """
    验证公共参数并提取必要信息
    :param params: 请求参数字典
    :return: (final_db_info, engine_type, engine, datasource_name, dbname)
    """
    # 校验 type 不为空，并且必须是 EngineTypeEnum 中定义的值
    if 'type' not in params or not params['type']:
        raise ValueError("type is required and must not be empty")
    else:
        # 获取枚举值列表（包含所有大小写形式）
        valid_types = [e.value.lower() for e in EngineTypeEnum]

        # 检查输入值是否在有效枚举值中（不区分大小写）
        if params['type'].lower() not in valid_types:
            raise ValueError(f"type must be one of: {', '.join(valid_types)}")

    # 校验 db_info 必须不为空，并且必须是 json 数组
    db_info_org = params.get('db_info')
    if not db_info_org:
        raise ValueError("db_info is required and must not be empty")

    try:
        db_info = json.loads(db_info_org)
        if not isinstance(db_info, list) or len(db_info) == 0:
            raise ValueError("db_info must be a non-empty JSON array")
        final_db_info = db_info[0]
    except json.JSONDecodeError as e:
        logger.error(f"nl2sql loads db_info error, db_info: {db_info_org},error:{e}")
        raise ValueError("db_info must be a valid JSON array string")

    engine_type = params.get('type')
    engine = ""
    # dcl 引擎 engine 必须要有值，调用 dlc mcp 需要
    if engine_type.lower() == EngineTypeEnum.DLC.value.lower():
        if 'data_engine_name' not in params or not params['data_engine_name']:
            raise ValueError("When dlc nl2sql pipeline data_engine_name is required and must not be empty")
        engine = params.get('data_engine_name')

    datasource_name = ""
    # dlc 和 TC_HOUSE_D 引擎 datasource_name 名称不能为空
    if (engine_type.lower() == EngineTypeEnum.DLC.value.lower() or
            engine_type.lower() == EngineTypeEnum.TC_HOUSE_D.value.lower()):
        if "CatalogName" not in final_db_info or not final_db_info.get('CatalogName'):
            raise ValueError("When dlc or TCHouseD nl2sql pipeline CatalogName is required and must not be empty")
        datasource_name = final_db_info.get('CatalogName')

    if "DbName" not in final_db_info or not final_db_info.get('DbName'):
        raise ValueError("Dbname is required and must not be empty")

    return final_db_info, engine_type, engine, datasource_name
