from typing import Dict, Any
import traceback

from infra.mcp.nl2sql.entities.generate_sql_entity import Metadata, DatabaseInfo
from infra.mcp.nl2sql.utils.verify_utils import validate_common_params
from common.logger.logger import logger
from infra.mcp.nl2sql.core.select_tables_pipeline import SelectTablesPipeline


def select_tables(params: Dict[str, Any]) -> Dict[str, Any]:
    """根据自然语言问题分析相关的数据库列

        参数:
            params (Dict[str, Any]): 包含以下键的字典参数:
                - app_id: 应用ID
                - sub_account_uin: 用户ID
                - trace_id: 追踪ID
                - data_engine_name->数据库引擎名称 原先的 engine
                - db_info: db 信息->[
                    {
                            "CatalogName": "", 原先的 datasource_name
                            "DbName": "customer_db",原先的 dbname
                    }
                ]
                - is_sampling: 是否采样（默认False）
                - mcp_url: MCP服务地址
                - type: 引擎类型
                - question: 自然语言问题
                - record_id: 上游定义，每次请求一个都是一个新的 ID，
        返回:
            Dict[str, Any]: 包含以下键的结果字典:
                - tables: 涉及的表列表

        异常:
            当参数缺失或处理失败时抛出异常并记录日志
        """
    try:
        logger.info(f"Start processing select tables params:{params}")

        # 提取元数据参数（用于系统追踪和日志记录）
        metadata = Metadata(
            app_id=params.get("app_id", ""),
            sub_account_uin=params.get("sub_account_uin", ""),
            trace_id=params.get("trace_id", ""),
            record_id=params.get("record_id", ""),
        )

        # 使用公共函数校验参数并提取信息
        final_db_info, engine_type, engine, datasource_name = validate_common_params(params)
        dbname = final_db_info.get('DbName')
        database_info = DatabaseInfo(
            engine=engine,
            datasource_name=datasource_name,
            dbname=dbname,
            is_sampling=params.get("is_sampling", False),  # 默认不开启采样
            mcp_url=params.get("mcp_url", {}),
            engine_type=engine_type,
            tables=set()
        )

        # 初始化Schema Linking处理器
        columns_semantic_llm = SelectTablesPipeline(question=params.get("question", ""), metadata=metadata,
                                                    database_info=database_info)
        # 执行Schema Linking分析
        tables = columns_semantic_llm.select_tables()

        logger.info(f"Select tables final result:{tables},trace_id:{metadata.trace_id}")

        # 格式化输出结果
        result = {
            "selected_tables": tables,
        }

        logger.info(f"Finish select tables params:{params}, "
                    f"found {tables}")
        return result
    except Exception as e:
        # 记录完整错误日志（包含输入参数和异常详情）
        logger.error(f"Select tables 处理失败: params:{params}, e:{e}", exc_info=True)
        raise  # 向上层抛出异常保持调用栈

