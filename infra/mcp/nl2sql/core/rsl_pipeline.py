from typing import List, Dict

from common.logger.logger import logger
from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.entities.generate_sql_entity import SQLGenerateData
from infra.mcp.nl2sql.llm.generate_prompt import get_user_prompt, get_system_prompt
from infra.mcp.nl2sql.llm.llm import llm_chat, embedding_texts
from infra.mcp.nl2sql.preprocessing.basic_entity_retrieval import EntityRetrieval
from infra.mcp.nl2sql.preprocessing.es_vector_store import Nl2SQLVectorStore, Nl2sqlEsQueryParams
from infra.mcp.nl2sql.utils.db_util import Column
from infra.mcp.nl2sql.utils.gpt_rsp_util import extract_first_json_block


class RSL:
    lsh: LSH
    question: str
    keywords: List[str]
    db_schema: Dict[str, List[Column]]
    ddl: Dict[str, str]
    trace_id: str
    vector_store: Nl2SQLVectorStore
    app_id: str
    datasource_name: str
    dbname: str
    engine_type: str
    business_terms: str

    def __init__(self, lsh: LSH, question: str, keywords: List[str], db_schema: Dict[str, List[Column]],
                 ddl: Dict[str, str], trace_id: str, vector_store: Nl2SQLVectorStore, app_id: str,
                 datasource_name: str, dbname: str, engine_type: str, business_terms: str = ""):
        self.lsh = lsh
        self.question = question
        self.keywords = keywords
        self.db_schema = db_schema
        self.ddl = ddl
        self.trace_id = trace_id
        self.vector_store = vector_store
        self.app_id = app_id
        self.datasource_name = datasource_name
        self.dbname = dbname
        self.engine_type = engine_type
        self.business_terms = business_terms

    def run(self) -> SQLGenerateData:
        logger.info(f"Start rsl pipeline,trace_id ={self.trace_id}")
        similar_entities = {}
        if self.lsh:
            entity_retrieval = EntityRetrieval(self.lsh, self.keywords, self.db_schema, self.question,self.trace_id)
            similar_entities = entity_retrieval.get_similar_entities()
            logger.info(f"rsl entity retrieval result ={similar_entities},trace_id={self.trace_id}")

        value_samples = {}
        if len(similar_entities):
            for table, table_samples in similar_entities.items():
                if not self.db_schema[table]:
                    continue
                value_samples[table] = table_samples

        examples = self._get_fewshot()
        schema_string = "\n".join([f"{k}: {v}" for k, v in self.ddl.items()])

        system_prompt = get_system_prompt(engine_type=self.engine_type, database_schema=schema_string,
                                          database_table_data_example=value_samples, task=self.question,
                                          examples=examples, database_name=self.dbname,
                                          business_terms=self.business_terms, data_source_name=self.datasource_name)
        llm_prompts = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": get_user_prompt(self.question)},
        ]
        rsp = llm_chat(llm_prompts)
        logger.info(f"RSL pipeline generation completed for question: {self.question},rsp = {rsp},"
                    f"trace_id={self.trace_id}")
        sql_data = extract_first_json_block(rsp)
        logger.info(f"Finished rsl pipeline,trace_id = {self.trace_id}")
        return SQLGenerateData(
            sql=sql_data.get("sql", "").rstrip(';') if isinstance(sql_data.get("sql", ""), str) else sql_data.get("sql",
                                                                                                                  ""),
            reasoning=sql_data.get("reasoning", ""),
            tables=sql_data.get("tables", []),
            db_schema=schema_string
        )

    def _get_fewshot(self):
        embeddings = embedding_texts([self.question])
        if not embeddings:
            logger.error(
                f"rsl get fewshot embedding_texts返回空数组，问题内容：'{self.question}'，trace_id={self.trace_id}")
            return ""
        question_emb = embeddings[0]
        script = {
            "source": f"1 / (1 + l2norm(params.queryVector, 'vector_question'))",
            "params": {
                "queryVector": question_emb
            }
        }
        params = Nl2sqlEsQueryParams(vector_field="vector_question", vector=question_emb, app_id=self.app_id,
                                     datasource_name=self.datasource_name, dbname=self.dbname, top_k=3, min_score=0.7,
                                     script=script, engine_type=self.engine_type, feedback=1)
        examples = self.vector_store.get_nl2sql_records_for_script(params)
        logger.info(f"rsl get examples,trace_id={self.trace_id},examples:{len(examples)}")
        prompt = ""
        if len(examples) == 0:
            return ""
        for example in examples:
            template_qa = "\n\n- {}\n{}"
            prompt += template_qa.format(
                example.question.replace("\n", " ").strip(),
                example.sql.replace("\n", " ").strip(),
            )
        return prompt

    def _get_columns_description(self) -> str:
        description = "#\n#"
        for table, columns in self.db_schema.items():
            for col in columns:
                description += f" {table}.`{col.Name}`: {col.Comment}\n#"
        return description
