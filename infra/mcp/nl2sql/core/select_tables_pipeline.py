from typing import Dict, List, Tuple

from common.logger.logger import logger
from common.share import error
from common.share.utils import detect_language
from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.entities.engine_type_enum import EngineTypeEnum
from infra.mcp.nl2sql.entities.generate_sql_entity import Metadata, DatabaseInfo
from infra.mcp.nl2sql.extract_keywords import extract_keywords
from infra.mcp.nl2sql.llm.llm import llm_chat
from infra.mcp.nl2sql.llm.select_tables_prompt import COLUMN_SELECTION_PROMPT
from infra.mcp.nl2sql.preprocessing.basic_entity_retrieval import EntityRetrieval
from infra.mcp.nl2sql.utils.db_util import (
    Column, DatabaseContext, create_database_context
)

from concurrent.futures import ThreadPoolExecutor, as_completed

from infra.mcp.nl2sql.utils.gpt_rsp_util import extract_first_json_block


class SelectTablesPipeline:
    """
    基于LLM的列语义处理类
    使用大模型直接分析问题与列的相关性

    属性:
        metadata (Metadata): 应用元数据信息
        database_info (DatabaseInfo): 数据库连接和结构信息
        question (str): 用户提出的自然语言问题
    """
    metadata: Metadata
    database_info: DatabaseInfo
    question: str

    def __init__(self, question: str, metadata: Metadata, database_info: DatabaseInfo):
        """初始化SQL生成器实例

        参数:
            question (str): 需要转换为SQL的自然语言问题
            metadata (Metadata): 包含应用追踪信息的元数据对象
            database_info (DatabaseInfo): 数据库连接和结构信息对象

        异常:
            ValueError: 当输入参数为空时抛出
        """
        # 参数有效性校验
        if not question or metadata is None or database_info is None:
            logger.error(
                "智能选表工具初始化参数为空 问题: %s, 元数据: %s, 数据库信息: %s",
                question, metadata, database_info
            )
            raise ValueError(error.ErrorCode.ParamError.value)

        # 初始化实例属性
        self.metadata = metadata
        self.database_info = database_info
        self.question = question
        self.relevance_threshold = 0.8  # 相关性阈值

    def select_tables(self) -> List[str]:
        """
        完整的问题到相关列处理流程
        Returns:
            Dict[str, List[Tuple[str, float]]]: 相关列信息 {表名: [(列名, 分数)]}
        """
        logger.info(f"开始 select tables 处理流程, question:{self.question}, "
                    f"engine_type:{self.database_info.engine_type}, trace_id:{self.metadata.trace_id}")

        try:
            # 1. 建立数据库连接
            database_context = self._get_database_context()
            tables = self._get_all_table_names(database_context)
            # 最多取 50 张表
            tables = tables[:50]

            # 2. 获取表DDL
            ddl = self._get_tables_ddl(database_context, tables, self.database_info.dbname,
                                       self.database_info.datasource_name)

            # 3. 获取数据库Schema
            db_schema = self._get_tables_schema(database_context, ddl)
            # 创建线程池
            with ThreadPoolExecutor(max_workers=2) as executor:
                # 提交LLM分析任务
                futures = {'llm': executor.submit(self._analyze_columns_with_llm, ddl)}

                # 提交LSH分析任务（如果启用采样）
                if self.database_info.isSampling:
                    futures['lsh'] = executor.submit(self._run_lsh_analysis, database_context, db_schema,tables)

                # 等待所有任务完成
                entities_columns = {}
                similar_entities = {}
                relevant_columns = {}

                for future in as_completed(futures.values()):
                    try:
                        result = future.result()
                        if future == futures.get('llm'):
                            relevant_columns = result
                        elif future == futures.get('lsh'):
                            entities_columns, similar_entities = result
                    except Exception as e:
                        task_name = [k for k, v in futures.items() if v == future][0]
                        logger.error(f"Select tables 执行{task_name} 任务时发生异常: {e},"
                                     f" trace_id:{self.metadata.trace_id}",exc_info=True)
                        # 如果是LSH任务失败，我们设置默认值，因为LLM任务可能还能提供部分结果
                        if future == futures.get('lsh'):
                            entities_columns = {}
                            similar_entities = {}
                        # 如果是LLM任务失败，我们设置默认值
                        elif future == futures.get('llm'):
                            relevant_columns = {}

            merged_keys = list({*entities_columns, *similar_entities, *relevant_columns})
            logger.info(f"Select tables 处理流程完成,select tables:{merged_keys},trace_id:{self.metadata.trace_id}")
            return merged_keys

        except Exception as e:
            logger.error(f"Select tables 处理流程失败 trace_id:{self.metadata.trace_id},error: {e}",exc_info=True)
            raise

    def _run_lsh_analysis(self, database_context: DatabaseContext, db_schema: Dict[str, List[Column]],tables:List[str])\
            -> Tuple[Dict, Dict]:
        """执行LSH分析并返回结果"""
        lsh = LSH(signature_size=100, n_gram=3, threshold=0.01, tables=tables,
                  db_name=self.database_info.dbname, engine_name=self.database_info.engine_name,
                  datasource_name=self.database_info.datasource_name,
                  database_context=database_context, trace_id=self.metadata.trace_id, app_id=self.metadata.app_id,
                  sub_account_uin=self.metadata.sub_account_uin, engine_type=self.database_info.engine_type)
        lsh.create_lsh()
        logger.info(f"Select tables initialize lsh finished,trace_id:{self.metadata.trace_id}")
        keywords = extract_keywords(self.question,self.metadata.trace_id)
        entity_retrieval = EntityRetrieval(lsh, keywords, db_schema, self.question, self.metadata.trace_id)
        entities_columns = entity_retrieval.get_similar_columns()
        similar_entities = entity_retrieval.get_similar_entities()
        logger.info(f"Select tables lsh analyze entities columns:{entities_columns.keys()},"
                    f" similar_entities:{similar_entities.keys()},trace_id:{self.metadata.trace_id}")
        return entities_columns, similar_entities

    def _analyze_columns_with_llm(self, ddl: Dict[str, str]) -> Dict[str, List[Tuple[str, float]]]:
        """
        使用LLM分析列与问题的相关性

        Args:
            ddl : 数据库建表语句

        Returns:
            Dict[str, List[Tuple[str, float]]]: 相关列信息 {表名: [(列名, 分数)]}
        """
        logger.info(f"Select tables 列相关性分析, question:{self.question}, trace_id:{self.metadata.trace_id}")

        # 构建LLM提示
        prompt = COLUMN_SELECTION_PROMPT.format(
            QUESTION=self.question,
            TABLE_SCHEMA=ddl,
            DB_ENGINE=self.database_info.engine_type,
            LANG=detect_language(self.question)
        )

        llm_prompts = [
            {"role": "user", "content": prompt},
        ]

        try:
            # 调用LLM
            response = llm_chat(llm_prompts)

            # 解析LLM响应
            result_data = extract_first_json_block(response)

            if not isinstance(result_data, dict) or "relevant_columns" not in result_data:
                logger.error(f"LLM响应格式错误: {result_data},trace_id:{self.metadata.trace_id}")
                return {}

            # 转换为标准格式
            relevant_columns = {}

            for col_info in result_data["relevant_columns"]:
                table_name = col_info["table"]
                column_name = col_info["column"]
                score = float(col_info["score"])

                # 验证分数和阈值
                if score >= self.relevance_threshold:
                    if table_name not in relevant_columns:
                        relevant_columns[table_name] = []
                    relevant_columns[table_name].append((column_name, score))

            # 按分数排序
            for table_name in relevant_columns:
                relevant_columns[table_name].sort(key=lambda x: x[1], reverse=True)

            logger.info(
                f"LLM列相关性分析完成, 找到相关列: {sum(len(cols) for cols in relevant_columns.values())},"
                f" trace_id={self.metadata.trace_id}")
            logger.info(f"Select tables llm analyze:{relevant_columns.keys()},trace_id:{self.metadata.trace_id}")
            return relevant_columns

        except Exception as e:
            logger.error(f"LLM列相关性分析失败: trace_id:{self.metadata.trace_id},e:{e}",exc_info=True)
            return {}

    def _get_database_context(self) -> DatabaseContext:
        """获取数据库连接上下文"""
        return create_database_context(
            self.database_info.engine_type,
            self.database_info.mcp_url
        )

    def _get_all_table_names(self, database_context: DatabaseContext) -> List[str]:

        table_names = database_context.list_all_db_tables({
            "Catalog": self.database_info.datasource_name,
            "Database": self.database_info.dbname,
        })
        return table_names

    def _get_tables_ddl(self, database_context: DatabaseContext, tables: List[str],
                        dbname: str, datasource_name: str) -> Dict[str, str]:
        """获取表的DDL语句"""
        logger.info(f"获取表DDL, tables:{tables}, trace_id:{self.metadata.trace_id}")

        ddl = database_context.get_tables_ddl(tables, dbname, datasource_name)

        if not ddl or len(ddl) < 1:
            logger.error(f"未找到数据表DDL, datasource_name:{datasource_name}, "
                         f"dbname:{dbname}, tables:{tables}, trace_id:{self.metadata.trace_id}")
            raise ValueError("未找到数据表")

        logger.info(f"表DDL获取成功, 表数量:{len(ddl)}, trace_id:{self.metadata.trace_id}")
        return ddl

    def _get_tables_schema(self, database_context: DatabaseContext, ddl: Dict[str, str]) -> Dict[str, List[Column]]:
        """获取表的Schema信息"""
        engine_type = self.database_info.engine_type
        logger.info(f"Select tables 获取表 Schema, engine_type:{self.database_info.engine_type}, "
                    f"trace_id:{self.metadata.trace_id}")

        db_schema = {}
        if engine_type.lower() == EngineTypeEnum.TC_HOUSE_D.value.lower():
            db_schema = database_context.get_tables_schema({
                "Catalog": self.database_info.datasource_name,
                "Database": self.database_info.dbname,
                "Tables": self.database_info.tables
            })
        elif engine_type.lower() == EngineTypeEnum.DLC.value.lower():
            db_schema = database_context.get_tables_schema(params=ddl)
        elif engine_type.lower() == EngineTypeEnum.ES.value.lower():
            db_schema = database_context.get_tables_schema(params={
                "Tables": self.database_info.tables
            })
        elif engine_type.lower() == EngineTypeEnum.TC_HOUSE_C.value.lower():
            db_schema = database_context.get_tables_schema(params={
                "Database": self.database_info.dbname,
                "Tables": self.database_info.tables
            })

        if not db_schema:
            logger.error(f"Select tables 未能获取表 Schema, engine_type:{engine_type}, trace_id:{self.metadata.trace_id}")
            raise ValueError("未能获取表Schema")

        logger.info(f"表Schema获取成功, 表数量:{len(db_schema)}, trace_id:{self.metadata.trace_id}")
        return db_schema
