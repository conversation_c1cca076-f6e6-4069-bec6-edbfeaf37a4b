from peewee import Model
from pydantic import BaseModel
from datetime import datetime, timezone
from typing import Optional


class Knowledge(BaseModel):
    file_id: str
    file_name: str
    file_size: float
    file_url: Optional[str]
    status: int  # -1:错误;0:处理中;1:可用
    type: int  # 0:文本
    create_user: str
    chunk_config: str
    knowledge_base_id: str
    source: Optional[int] = 0
    is_show_case: Optional[int] = 0
    create_time: datetime = datetime.now(timezone.utc)
    update_time: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }