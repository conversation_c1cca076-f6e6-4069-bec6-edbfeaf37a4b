from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class UserSession(BaseModel):
    """用户会话实体类"""
    sub_account_uin: str  # 用户ID
    session_id: str  # 会话ID
    session_title: str  # 会话标题
    db_info: Optional[str] = None  # 会话db信息
    run_record: Optional[str] = None  # 运行中的聊天请求
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间
    deleted_at: Optional[datetime] = None  # 删除时间

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
