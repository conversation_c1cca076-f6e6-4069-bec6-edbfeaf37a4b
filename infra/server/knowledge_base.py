import asyncio
import base64
import json
from datetime import timezone

from fastapi import Response, status, Request, APIRouter
from fastapi.responses import StreamingResponse
import uuid
from common.database.database import global_metadata_db_pool
from common.tencentcloud import ai_search_index
from common.tencentcloud.ai_search_index import process_document_semantic_preview, \
    DocumentChunk, DocumentChunkConfig, process_document_rule_preview
from infra.adapter.knowledge_list_adapter import KnowledgeListAdapter
from infra.adapter.task_list_adapter import TaskListAdapter
from infra.adapter.user_search_config_adapter import UserSearchConfigAdapter
from infra.domain.knowledge_list_entity import Knowledge
from infra.domain.task_list_entity import TaskList
from infra.memory.knowledge_operator import KnowledgeOperator
from infra.memory.semantic_operator import Semantic<PERSON>perator, SemanticData, Scope
from infra.server.model import *

router = APIRouter()
mysql_pool = global_metadata_db_pool()


@router.post("/query_knowledge_list")
def query_knowledge_list(knowledge_params: RequestKnowledgeParams, request: Request, response: Response):
    logger.info(f"query_knowledge_list - {knowledge_params}")
    filter_conditions = None
    order_conditions = None
    knowledge_adapter = KnowledgeListAdapter(mysql_pool)
    ctx = request.state.context
    model = knowledge_adapter.persistence.get_model('knowledge_list')
    try:
        if knowledge_params.Filters:
            filter_conditions = [
                f.to_peewee_condition(model, KNOWLEDGE_TYPE_MAP)
                for f in knowledge_params.Filters
            ]
        if knowledge_params.Sorts:
            order_conditions = [s.to_peewee_order(model, KNOWLEDGE_TYPE_MAP) for s in knowledge_params.Sorts]
    except Exception as e:
        logger.error(f"query_knowledge_list error: {e}", exc_info=True)
        response.status_code = status.HTTP_400_BAD_REQUEST
        return gw_error(response.status_code, ErrorCode.ParamError, str(e))

    offset = (knowledge_params.Page - 1) * knowledge_params.PageSize
    total, knowledge_list = knowledge_adapter.get_knowledge_list(ctx, filter_conditions, order_conditions, offset,
                                                                 knowledge_params.PageSize)
    return KnowledgeResponse(
        Status=status.HTTP_200_OK,
        KnowledgeInfoList=[KnowledgeInfo(
            FileId=knowledge.file_id,
            FileName=knowledge.file_name,
            FileUrl=knowledge.file_url,
            FileSize=knowledge.file_size,
            Status=knowledge.status,
            Source=knowledge.source,
            IsShowCase=knowledge.is_show_case or 0,
            ChunkConfig=json.loads(knowledge.chunk_config),
            Type=knowledge.type,
            CreateUser=("DataAgent" if knowledge.is_show_case == 1 else knowledge.create_user),
            CreateTime=knowledge.create_time,
        ) for knowledge in knowledge_list],
        Total=total
    )


# 智能检索和自定义检索，前端都会传递delimiter和chunkOverlap
def decode_delimiter(delimiter):
    """解码Base64并确保控制字符不被转义"""
    try:
        # 先解码Base64
        decoded_bytes = base64.b64decode(delimiter)
        # 将字节转换为字符串（可能包含转义字符）
        return decoded_bytes.decode('utf-8')
    except Exception as e:
        logger.warning(f"Failed to decode delimiter: {delimiter}, error: {e}")
        return delimiter


@router.post("/create_knowledge_task")
def create_knowledge_task(knowledge_task: KnowledgeTaskBatch, request: Request, response: Response):
    logger.info(f"create_knowledge_task - {knowledge_task}")
    ctx = request.state.context

    # 参数校验
    config = knowledge_task.Config
    if config.ChunkType not in [0, 1]:
        response.status_code = status.HTTP_400_BAD_REQUEST
        return gw_error(response.status_code, ErrorCode.ParamError, "chunk type must be 0 (custom) or 1 (smart)")

    if config.Delimiters:
        config.Delimiters = [
            decode_delimiter(d) for d in
            getattr(config, 'Delimiters', []) or ["\n\n", "\n", "。", "！", "？", "，", ""]
        ]

    if config.ChunkType == 1:  # 智能切片
        if not hasattr(config, 'MaxChunkSize') or config.MaxChunkSize < 1000:
            config.MaxChunkSize = 4800  # 默认值
    else:  # 自定义切片
        if not hasattr(config, 'MaxChunkSize') or config.MaxChunkSize <= 0:
            config.MaxChunkSize = 1000  # 默认值
        if not hasattr(config, 'Delimiters') or not config.Delimiters:
            config.Delimiters = ["\n\n", "\n", "。", "！", "？", "，", ""]
        if not hasattr(config, 'ChunkOverlap'):
            config.ChunkOverlap = 0

    try:
        knowledge_list_adapter = KnowledgeListAdapter(mysql_pool)
        task_list_adapter = TaskListAdapter(mysql_pool)

        # 准备批量知识任务数据
        knowledge_tasks = [
            Knowledge(
                file_id=doc.FileId,
                file_name=doc.FileName,
                file_size=doc.FileSize,
                file_url=doc.FileUrl,
                knowledge_base_id=knowledge_task.KnowledgeBaseId,
                source=doc.Source,
                is_show_case=doc.IsShowCase,
                chunk_config=json.dumps(knowledge_task.Config.model_dump()),
                type=0,  # 默认类型
                status=0,  # 处理中
                create_user=ctx.sub_account_uin,
                create_time=datetime.now(),
            ) for doc in knowledge_task.Documents
        ]

        # 准备批量任务数据
        tasks = [
            TaskList(
                task_id=str(uuid.uuid4()),
                app_id=ctx.app_id,
                file_id=doc.FileId,
                knowledge_base_id=knowledge_task.KnowledgeBaseId,
                file_url=doc.FileUrl,
                task_type=knowledge_task.Config.ChunkType,
                status=0,
                create_time=datetime.now(),
                task_params=json.dumps(knowledge_task.Config.model_dump()),
            ) for doc in knowledge_task.Documents
        ]
        with mysql_pool.pool_db.atomic():
            # 批量创建知识任务
            if not knowledge_list_adapter.batch_create_knowledge_tasks(ctx, knowledge_tasks):
                raise Exception("批量创建知识任务失败")
            # 批量创建任务
            if not task_list_adapter.batch_create(tasks):
                raise Exception("创建任务失败")
        return CommonResponse(Status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error create_knowledge_task error : {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "create knowledge task failed")
    finally:
        mysql_pool.pool_db.manual_close()


@router.post("/update_knowledge_task")
def update_knowledge_task(knowledge_task: UpdateKnowledgeTask, request: Request, response: Response):
    logger.info(f"update_knowledge_task - {knowledge_task}")
    ctx = request.state.context

    # 参数校验
    config = knowledge_task.Config
    if config.ChunkType not in [0, 1]:
        response.status_code = status.HTTP_400_BAD_REQUEST
        return gw_error(response.status_code, ErrorCode.ParamError, "chunk type must be 0 (custom) or 1 (smart)")

    if config.Delimiters:
        config.Delimiters = [
            decode_delimiter(d) for d in
            getattr(config, 'Delimiters', []) or ["\n\n", "\n", "。", "！", "？", "，", ""]
        ]
    if config.ChunkType == 1:  # 智能切片
        if not hasattr(config, 'MaxChunkSize') or config.MaxChunkSize < 1000:
            config.MaxChunkSize = 4800  # 默认值
    else:  # 自定义切片
        if not hasattr(config, 'MaxChunkSize') or config.MaxChunkSize <= 0:
            config.MaxChunkSize = 1000  # 默认值
        if not hasattr(config, 'Delimiters') or not config.Delimiters:
            config.Delimiters = ["\n\n", "\n", "。", "！", "？", "，", ""]
        if not hasattr(config, 'ChunkOverlap'):
            config.ChunkOverlap = 0
    try:
        knowledge_list_adapter = KnowledgeListAdapter(mysql_pool)
        task_list_adapter = TaskListAdapter(mysql_pool)
        update_knowledge = knowledge_list_adapter.get_knowledge_by_file_id(knowledge_task.FileId)
        if update_knowledge is None:
            logger.error(f"Error update_knowledge_task error : knowledge_task is None")
            response.status_code = status.HTTP_400_BAD_REQUEST
            return gw_error(response.status_code, ErrorCode.NotFoundError, "file not exist")
        if update_knowledge.status == 0:
            logger.error(f"Error update_knowledge_task error : knowledge_task is already processed")
            response.status_code = status.HTTP_400_BAD_REQUEST
            return gw_error(response.status_code, ErrorCode.ParamError, "knowledge_task is already processed")
        task = TaskList(
            task_id=str(uuid.uuid4()),
            app_id=ctx.app_id,
            file_id=update_knowledge.file_id,
            knowledge_base_id=update_knowledge.knowledge_base_id,
            file_url=update_knowledge.file_url,
            task_type=knowledge_task.Config.ChunkType,
            status=0,
            create_time=datetime.now(),
            task_params=json.dumps(knowledge_task.Config.model_dump()),
        )
        with mysql_pool.pool_db.atomic():
            knowledge_list_adapter.update_config(knowledge_task.FileId, 0,
                                                 json.dumps(knowledge_task.Config.model_dump()))
            if not task_list_adapter.create(task):
                raise Exception("重跑任务失败")
        return CommonResponse(Status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error update_knowledge_task error : {e}", exc_info=True)
        return gw_error(response.status_code, ErrorCode.InternalError, "update knowledge task failed")
    finally:
        mysql_pool.pool_db.manual_close()


# 定义信号量，限制并发数为20
semaphore = asyncio.Semaphore(20)


async def generate_chunk_preview_streaming_response(knowledge_task: KnowledgeTask):
    """生成流式预览响应，定期发送空消息以避免超时"""
    async with semaphore:  # 限制并发
        try:
            # 模拟异步任务执行
            async def async_task(document_chunk: DocumentChunk, chunk_type: int):
                if chunk_type == 0:
                    chunks = await asyncio.to_thread(process_document_rule_preview, document_chunk)
                    return {"Status": status.HTTP_200_OK,
                            "Chunks": chunks}
                if chunk_type == 1:
                    chunks = await asyncio.to_thread(process_document_semantic_preview, document_chunk)
                    return {"Status": status.HTTP_200_OK,
                            "Chunks": chunks}
                else:
                    raise Exception("不支持的预览类型")

            task = asyncio.create_task(async_task(DocumentChunk(
                FileUrl=knowledge_task.FileUrl,
                # FileStartPageNumber=1,
                # FileEndPageNumber=10,
                ChunkConfig=DocumentChunkConfig(
                    MaxChunkSize=knowledge_task.Config.MaxChunkSize,
                    Delimiters=knowledge_task.Config.Delimiters,
                    ChunkOverlap=knowledge_task.Config.ChunkOverlap,
                )
            ), knowledge_task.Config.ChunkType))

            # 定期发送空消息
            while not task.done():
                yield ": heartbeat\n\n"
                await asyncio.sleep(1)

            result = await task
            yield f"event: result\ndata: {json.dumps(result, ensure_ascii=False)}\n\n"
            yield "event: close\ndata: {}\n\n"

        except Exception as e:
            logger.error(f"生成流式响应失败: {e}")
            yield f"event: error\ndata: {json.dumps({'Code': 'InternalError', 'Message': str(e)}, ensure_ascii=False)}\n\n"
            yield "event: close\ndata: {}\n\n"


@router.post("/knowledge_chunk_preview")
async def preview_knowledge_chunk(knowledge_task: KnowledgeTask, request: Request, response: Response):
    logger.info(f"preview_knowledge_chunk - {knowledge_task}")
    ctx = request.state.context
    if knowledge_task.Config.ChunkType not in [0, 1]:
        return gw_error(response.status_code, ErrorCode.ParamError, "unsupported chunk type")
    if knowledge_task.Config.ChunkType == 0:
        def process_delimiter(delimiter):
            try:
                decoded_bytes = base64.b64decode(delimiter)
                delimiter = decoded_bytes.decode('utf-8')
            except Exception:
                pass  # 保持原值（非Base64编码或解码失败时）
            # 统一转义字符处理
            return delimiter.replace('\\n\\n', '\n\n').replace('\\n', '\n')

        # 处理分隔符列表（包含默认值逻辑）
        default_delimiters = ["\n\n", "\n", "。", "！", "？", "，", ""]
        knowledge_task.Config.Delimiters = [process_delimiter(d) for d in
                                            knowledge_task.Config.Delimiters or default_delimiters] or default_delimiters
        if not knowledge_task.Config.MaxChunkSize:
            knowledge_task.Config.MaxChunkSize = 1000
            knowledge_task.Config.ChunkOverlap = 200
    if knowledge_task.Config.ChunkType == 1:
        if not knowledge_task.Config.MaxChunkSize >= 1000:
            response.status_code = status.HTTP_400_BAD_REQUEST
            return gw_error(response.status_code, ErrorCode.ParamError, "max chunk size must be less than 1000")
    try:
        return StreamingResponse(
            generate_chunk_preview_streaming_response(knowledge_task),
            media_type="text/event-stream"
        )
    except Exception as e:
        logger.error(f"preview_knowledge_chunk error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "generate chunk preview failed")


@router.post("/query_chunk_list")
def query_chunk_list(params: RequestChunkListParams, request: Request, response: Response):
    """
    文档切片查询接口
    """
    logger.info(f"query_chunk_list request: {params}")
    ctx = request.state.context
    knowledge_operator = KnowledgeOperator.get_instance(ctx.app_id)
    try:
        # 调用知识库操作类查询切片
        result = knowledge_operator.search_knowledge_records(
            file_id=params.FileId,
            content=params.Content,
            page=params.Page,
            page_size=params.PageSize,
        )

        # 转换结果为Chunk格式
        chunks = [
            {
                "Id": record.chunk_id,
                "Content": record.content,
                "Size": len(record.content)
            }
            for record in result["data"]
        ]

        return {
            "Status": status.HTTP_200_OK,
            "Total": result["total"],
            "Chunks": chunks
        }
    except Exception as e:
        logger.error(f"query_chunk_list error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "query chunk list failed")


@router.post("/modify_chunk")
def modify_chunk(params: ChunkModify, request: Request, response: Response):
    """
    文档切片编辑接口
    """
    logger.info(f"modify_chunk request: {params}")
    ctx = request.state.context
    knowledge_operator = KnowledgeOperator.get_instance(ctx.app_id)
    try:
        # 调用知识库操作类更新切片
        knowledge_operator.update_knowledge_content(
            file_id=params.FileId,
            chunk_id=params.ChunkId,
            new_content=params.Content,
            new_content_embedding=ai_search_index.get_text_embeddings([params.Content])[0]["content_embedding"],
        )

        return {"Status": status.HTTP_200_OK}
    except Exception as e:
        logger.error(f"modify_chunk error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "update chunk failed")


@router.post("/delete_chunk")
def delete_chunk(params: ChunkDelete, request: Request, response: Response):
    """
    文档切片删除接口
    """
    logger.info(f"delete_chunk request: {params}")
    ctx = request.state.context
    try:
        knowledge_operator = KnowledgeOperator.get_instance(ctx.app_id)
        # 调用知识库操作类删除切片
        knowledge_operator.batch_delete_knowledge_chunks(
            file_id=params.FileId,
            chunk_ids=params.ChunkIds
        )

        return {"Status": status.HTTP_200_OK}
    except Exception as e:
        logger.error(f"delete_chunk error: {e}")
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "delete chunk failed")


@router.post("/delete_file")
def delete_file(params: FileDelete, request: Request, response: Response):
    """文档切片删除接口"""
    ctx = request.state.context
    knowledge_operator = KnowledgeOperator.get_instance(ctx.app_id)
    try:
        knowledge_adapter = KnowledgeListAdapter(mysql_pool)
        knowledge_adapter.delete_knowledge_by_file_ids(file_ids=params.FileIds)
        # 调用知识库操作类删除文件
        knowledge_operator.delete_knowledge_by_files(
            file_ids=params.FileIds,
        )
        return {"Status": status.HTTP_200_OK}
    except Exception as e:
        logger.error(f"delete_file error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "delete file failed")


@router.post("/add_chunk")
def add_chunk(params: ChunkCreate, request: Request, response: Response):
    """
    文档切片新增接口
    """
    logger.info(f"add_chunk request: {params}")
    ctx = request.state.context
    knowledge_operator = KnowledgeOperator.get_instance(ctx.app_id)
    try:
        chunk_id = str(uuid.uuid4())
        # 调用知识库操作类新增切片
        chunk_id = knowledge_operator.insert_knowledge_at_position(
            file_id=params.FileId,
            knowledge_data={
                "chunk_id": chunk_id,
                "file_id": params.FileId,
                "content": params.Content,
                "content_embedding": ai_search_index.get_text_embeddings([params.Content])[0]["content_embedding"],
            }, before_chunk_id=params.BeforeChunkId,
            after_chunk_id=params.AfterChunkId,
            insert_position=params.InsertPos)
        return {
            "Status": status.HTTP_200_OK,
            "ChunkId": chunk_id
        }
    except Exception as e:
        logger.error(f"add_chunk error: {e}")
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, "ADD_CHUNK_ERROR")


@router.post("/query_knowledge_config")
def query_knowledge_config(params: QueryKnowledgeConfig, request: Request,
                           response: Response) -> QueryKnowledgeConfigResponse:
    logger.info(f"query_knowledge_config - {params}")
    try:
        ctx = request.state.context
        adapter = UserSearchConfigAdapter(mysql_pool)
        config = adapter.get_by_app_id(ctx.app_id)

        return QueryKnowledgeConfigResponse(
            Status=status.HTTP_200_OK,
            SearchConfig=SearchConfig(
                Type=config.search_type,
                Num=config.recall_num,
                EmbeddingWeight=config.embedding_weight,
                Rerank=config.rerank_status
            )
        )
    except Exception as e:
        logger.error(f"query_knowledge_config error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "query knowledge config failed")


@router.post("/modify_knowledge_config")
def modify_knowledge_config(params: ModifyKnowledgeConfig, request: Request,
                            response: Response) -> ModifyKnowledgeConfigResponse:
    logger.info(f"modify_knowledge_config - {params}")
    try:
        ctx = request.state.context
        adapter = UserSearchConfigAdapter(mysql_pool)

        # 检查配置是否存在
        if not adapter.get_by_app_id(ctx.app_id):
            # 不存在则创建默认配置
            adapter.create(ctx.app_id)

        success = adapter.update(
            ctx.app_id,
            search_type=params.SearchConfig.Type,
            recall_num=params.SearchConfig.Num,
            embedding_weight=params.SearchConfig.EmbeddingWeight,
            rerank_status=params.SearchConfig.Rerank
        )

        if not success:
            raise Exception("更新配置失败")

        return ModifyKnowledgeConfigResponse(Status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"modify_knowledge_config error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "update knowledge config failed")


@router.post("/query_semantic_list")
async def query_semantic_list(params: QuerySemanticListParams, request: Request,
                              response: Response) -> QuerySemanticListResponse:
    ctx = request.state.context
    try:
        semantic_operator = SemanticOperator.get_instance(ctx.app_id)
        result = semantic_operator.get_semantics_with_pagination(
            app_id=ctx.app_id,
            page=params.Page,
            page_size=params.PageSize
        )
        semantic_config_list = [
            SemanticConfig(
                TermId=data.term_id,
                Term=data.term,
                Definition=data.definition or "",
                Synonyms=data.synonyms or [],
                Scope=[f"{scope.level1}.{scope.level2}.{scope.level3}" for scope in data.scope]
            ) for data in result["data"]
        ]
        return QuerySemanticListResponse(
            Status=status.HTTP_200_OK,
            SemanticConfigList=semantic_config_list,
            Total=result["total"]
        )
    except Exception as e:
        logger.error(f"query semantic list error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "query semantic list failed")


@router.post("/modify_semantic")
async def modify_semantic(params: ModifySemanticParams, request: Request, response: Response) -> CommonResponse:
    ctx = request.state.context
    try:
        semantic_operator = SemanticOperator.get_instance(ctx.app_id)
        scope = None
        if params.Scope:
            scope = [
                Scope(
                    level1=scope.split(".")[0],
                    level2=scope.split(".")[1],
                    level3=scope.split(".")[2]
                )
                for scope in params.Scope
            ]
        update_data = SemanticData(
            app_id=ctx.app_id,
            term_id=params.TermId,
            term=params.Term,
            definition=params.Definition,
            synonyms=params.Synonyms,
            scope=scope,
            update_time=datetime.now(timezone.utc)
        )
        updated_count = semantic_operator.update_semantic_data(
            app_id=ctx.app_id,
            term_id=params.TermId,
            update_data=update_data
        )
        if updated_count == 0:
            raise Exception("未找到匹配的语义配置")
        return CommonResponse(Status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"modify semantic error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "update semantic failed")


@router.post("/add_semantic")
async def add_semantic(params: AddSemanticParams, request: Request, response: Response) -> CommonResponse:
    ctx = request.state.context
    try:
        semantic_operator = SemanticOperator.get_instance(ctx.app_id)
        params.Scope = params.Scope or ["*.*.*"]
        semantic_data = SemanticData(
            term_id=str(uuid.uuid4()),
            app_id=ctx.app_id,
            term=params.Term,
            definition=params.Definition,
            synonyms=params.Synonyms,
            scope=[Scope(level1=scope.split(".")[0], level2=scope.split(".")[1], level3=scope.split(".")[2]) for scope
                   in params.Scope],
            create_time=datetime.now(timezone.utc)
        )
        semantic_operator.save_semantic_data(semantic_data)
        return CommonResponse(Status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"新增语义配置失败: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, "ADD_SEMANTIC_ERROR")


@router.post("/delete_semantic")
async def delete_semantic(params: DeleteSemanticParams, request: Request, response: Response) -> CommonResponse:
    ctx = request.state.context
    try:
        semantic_operator = SemanticOperator.get_instance(ctx.app_id)
        semantic_operator.delete_semantic_datas(
            app_id=ctx.app_id,
            term_ids=params.TermIds
        )
        return CommonResponse(Status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"删除语义配置失败: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, "DELETE_SEMANTIC_ERROR")


@router.post("/update_file")
def delete_file(params: FileUpdate, request: Request, response: Response):
    ctx = request.state.context
    knowledge_adapter = KnowledgeListAdapter(mysql_pool)
    try:
        knowledge_adapter.update_knowledge(params.FileId, params.FileName)
        return {"Status": status.HTTP_200_OK}
    except Exception as e:
        logger.error(f"update file error: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "update file failed")
