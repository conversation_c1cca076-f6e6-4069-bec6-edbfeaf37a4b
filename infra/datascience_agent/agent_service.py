# agent/datascience_agent/agent_service.py
import logging
import json
import asyncio
import os # Added for os.getenv for DBNAME and DATASOURCE_NAME fallbacks
from typing import Dict, Any, Optional, AsyncIterator, List

from common.trace.trace import traceable
from common.elements.agent_event import (
    AgentEvent, MessageEvent, ThinkEvent, TextEvent, TaskListEvent, ErrorEvent, FinalSummaryEvent, JupyterEvent
)
from common.share.config import appConfig
from infra.datascience_agent.graph_orchestrator import build_graph
from infra.datascience_agent.state import AgentState, get_task_category
from infra.datascience_agent.utils.data_loader import DataLoader
from infra.datascience_agent.utils.openai_client import OpenAIClient
from infra.datascience_agent.agents.executor.executor import ExecutorAgent
from infra.datascience_agent.agents.intent_recognizer.intent_schemas import IntentSlot
from infra.datascience_agent.agents.intent_recognizer.intent_recognizer import IntentRecognizer
from infra.mcp.manager.mcp_manager import MCPManager
from infra.datascience_agent.agents.mock_stream import MockAgentStreamer
from common.share.context import Context
from common.share.stream_param import StreamGenerationParams # For type hinting or direct use if needed
from common.share.error import ErrorCode
from common.logger.logger import logger

class AgentService:
    def __init__(self, mcp_manager: MCPManager): # MCPManager is now expected at init
        logger.info("initializing AgentService...")
        logger.info(f"appConfig: {appConfig}")
        logger.info(f"pwd: {os.getcwd()}")
        try:
            self.llm_client = OpenAIClient(
                api_key=appConfig.common.llm.api_key,
                base_url=appConfig.common.llm.base_url,
                model=appConfig.common.llm.model_name,
                temperature=appConfig.common.llm.temperature,
            )
        except ValueError as e:
            logger.error(f"Fatal: Failed to initialize OpenAIClient: {e}. Interface cannot start.")
            raise
        
        self.mcp_manager = mcp_manager # Store the passed MCPManager
        self.data_loader = DataLoader(self.mcp_manager) # Pass MCPManager to DataLoader

        self.executor_agent = ExecutorAgent(llm_client=self.llm_client, mcp_manager=self.mcp_manager)
        self.app = build_graph(llm_client=self.llm_client, executor_agent=self.executor_agent)
        self.session_states: Dict[str, AgentState] = {}
        self.initial_graph_state_template: AgentState = self._get_initial_state_template()
        self.session_id = None
        self.record_id = None
        
        # 管理活跃的graph stream任务（用于真正的立即取消）
        self.active_graph_tasks: Dict[str, asyncio.Task] = {}
        
        logger.info("AgentService initialized successfully.")

    def _get_initial_state_template(self) -> AgentState:
        # Schema will be loaded per session/stream now, not globally at init template
        initial_state: AgentState = {
            "current_user_input": None,
            "conversation_history": [],
            "intent_recognizer_slot_state": IntentSlot().model_dump(),
            "final_summary_content": None,
            "jupyter_events": [],
            "database_schema": None,
            # "all_available_tables": None,
            # "selected_tables": None,
            # "needs_table_selection": False,
            "ctx": None,  # 添加ctx字段
            "detected_language": None,  # 添加语言检测字段

            "user_feedback_on_last_result": None,
            # "is_feedback_request": False,
            "identified_intent_name": None,
            "identified_intent_entities": None,
            "needs_clarification": True,
            # "clarification_question": None,
            "current_plan": None,
            "execution_error": None,
            "final_output_for_user": None,
            # "available_data_sources": [],
            "active_dataset_id": None,
            # "all_available_tables": None,
            "mcp_manager": self.mcp_manager,
            "stop_requested": False,  # 添加停止标志初始化
        }
        return initial_state
    
    def _get_session_state(self, session_id: str) -> AgentState:
        if session_id not in self.session_states:
            logger.info(f"creating new session: {session_id}")
            new_state = self.initial_graph_state_template.copy()
            self.session_states[session_id] = new_state

        current_state = self.session_states[session_id]
        return current_state

    def _save_session_state(self, session_id: str, state: AgentState) -> None:
        self.session_states[session_id] = state

    def request_stop(self, session_id: str) -> bool:
        """请求停止指定session的执行
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否成功设置停止标志
        """
        try:
            current_state = self._get_session_state(session_id)
            current_state['stop_requested'] = True
            self._save_session_state(session_id, current_state)
            logger.info(f"🛑 Stop requested for session: {session_id}")
            
            # 同时取消活跃的graph任务
            if session_id in self.active_graph_tasks:
                task = self.active_graph_tasks[session_id]
                if not task.done():
                    task.cancel()
                    logger.info(f"🛑 Cancelled active graph task for session: {session_id}")
            
            return True
        except Exception as e:
            logger.error(f"Failed to request stop for session {session_id}: {e}")
            return False

    @traceable(name="stream_chat")
    async def stream_chat(
        self,
        ctx: Context, 
        query: str,
        session_id: str,
        record_id: str,
        dataset_id: Optional[str] = None, 
        chat_params: Optional[StreamGenerationParams] = None,
        mcp_manager: Optional[MCPManager] = None, 
    ) -> AsyncIterator[AgentEvent]:
        logger.info(f"starting stream_chat for session_id {session_id}, record_id {record_id}")

        if not mcp_manager:
            logger.error("mcp_manager not configured")

            yield ErrorEvent(
                err_code=ErrorCode.ConfigurationError,
                err_message="mcp_manager not configured"
            )
            return

        self.mcp_manager = mcp_manager
        self.session_id = session_id
        self.record_id = record_id

        current_state = self._get_session_state(session_id)
        current_state['current_user_input'] = query
        current_state['final_output_for_user'] = None
        current_state['execution_error'] = None
        current_state['mcp_manager'] = self.mcp_manager
        current_state['ctx'] = ctx  # 设置ctx到状态中
        current_state['session_id'] = session_id  # 确保session_id在状态中可用
        current_state['stop_requested'] = False
        # if current_state['final_summary_content'] is not None:
        #     current_state['final_summary_content'] = None
        #     current_state['jupyter_events'] = []
        #     current_state['intent_recognizer_slot_state'] = IntentSlot().model_dump()
            
        #     current_state['final_output_for_user'] = None
        #     current_state['execution_error'] = None
            
        
        # 每次都需要设置这些字段，确保当前调用的上下文正确
        current_state['mcp_manager'] = self.mcp_manager
        current_state['ctx'] = ctx
        

        # 从chat_params中获取db_table信息并存储到database_schema
        if chat_params and chat_params.db_table:
            current_state['database_schema'] = chat_params.db_table
            logger.info(f"从chat_params加载database_schema: {len(chat_params.db_table)}个表")
        
        if current_state.get('stop_requested'):
            logger.info(f" Stop request detected at start of stream_chat for session {session_id}")
            yield MessageEvent(content="任务已根据您的请求停止。")
            current_state['final_output_for_user'] = "任务已根据您的请求停止。"
            current_state['stop_requested'] = False  # 重置标志
            self._save_session_state(session_id, current_state)
            return
        
        logger.info(f"current_state: {current_state}")

        # quit logic
        if query.lower() in ["quit", "exit", "bye", "end chat", "再见"]:
            yield TextEvent()
            yield MessageEvent(content="再见！对话已结束。")

            current_state['identified_intent_name'] = "end_conversation"
            current_state['final_output_for_user'] = "再见！对话已结束。"
            current_state['needs_clarification'] = False

            recognizer = IntentRecognizer(llm_client=self.llm_client)
            slot_updates_for_quit = {
                "stage": 3,
                "task_type": "end_conversation",
                "task_description": "User requested to end chat."
            }
            recognizer.intent_slot.update_slot(slot_updates_for_quit)
            current_state['intent_recognizer_slot_state'] = recognizer.get_current_slot_state()
            self._save_session_state(session_id, current_state)
            
            return
        
        try:

            # 初始化IntentRecognizer    
            slot_state = current_state.get('intent_recognizer_slot_state', {})
            recognizer = IntentRecognizer(
                llm_client=self.llm_client, 
                initial_slot_data=slot_state
            )


            # 传入conversation_history到process_query
            conversation_history = current_state.get('conversation_history', [])
            
            async for event in recognizer.process_query(query, conversation_history=conversation_history, ctx=ctx, state=current_state):
                if event.event_type == "message":
                    if recognizer.get_task_category() == "document_query":
                        continue

                yield event

            # 1. 添加user input (如果不存在)
            if not conversation_history or \
               conversation_history[-1].get("content") != query or \
               conversation_history[-1].get("role") != "user":
                current_state['conversation_history'].append({"role": "user", "content": query})


            # 2. 添加assistant response (使用full_message_to_user)
            full_message_to_user = recognizer.get_final_user_facing_text()
            if full_message_to_user and full_message_to_user.strip():
                # 检查是否已经存在相同的assistant回复，避免重复
                if not conversation_history or \
                   conversation_history[-1].get("content") != full_message_to_user or \
                   conversation_history[-1].get("role") != "assistant":
                    current_state['conversation_history'].append({"role": "assistant", "content": full_message_to_user})

            
            # 🌐 每次都从recognizer获取最新的语言检测结果并保存到state
            detected_language = recognizer.get_detected_language()
            if detected_language:
                current_state['detected_language'] = detected_language
                logger.info(f"🌐 AgentService: 更新语言状态到state: {detected_language}")
            else:
                # 如果检测失败，使用默认语言
                current_state['detected_language'] = 'zh'
                logger.info(f"🌐 AgentService: 语言检测失败，使用默认语言: zh")
            
            current_state['intent_recognizer_slot_state'] = recognizer.get_current_slot_state()
            current_slot_dict = recognizer.get_current_slot_state()
            content_from_slot = current_slot_dict.get('content', {})
            task_info_from_slot = content_from_slot.get('task')
            logger.info(f"current_state2: {current_state}")
            if recognizer.is_intent_recognition_complete():
                current_state['needs_clarification'] = False
                # current_state['clarification_question'] = None
                current_state['identified_intent_name'] = task_info_from_slot.get('category') if task_info_from_slot else 'unknown_intent_at_stage3'
                current_state['identified_intent_entities'] = content_from_slot.get('metadata', {})
                
                
                if current_state['identified_intent_name'] != "end_conversation":
                    graph_input_state = current_state.copy()
                    graph_input_state['conversation_history'] = list(current_state.get('conversation_history', []))
                    graph_input_state['jupyter_events'] = list(current_state.get('jupyter_events', []))
                    graph_input_state['mcp_manager'] = self.mcp_manager

                    logger.debug(f"AgentService: Streaming graph with mcp_manager: {bool(graph_input_state.get('mcp_manager'))}")
                    
                    # 使用队列实现真正的流式处理 + 立即停止
                    event_queue = asyncio.Queue(100)
                    
                    async def graph_producer():
                        """生产者：将graph stream事件放入队列"""
                        try:
                            async for event in self._execute_graph_stream(graph_input_state, current_state, session_id):
                                # 在每次put前检查是否被取消
                                latest_state = self._get_session_state(session_id)
                                if latest_state.get('stop_requested'):
                                    logger.info(f"🛑 Producer detected stop request, breaking early for session: {session_id}")
                                    break
                                await event_queue.put(event)
                        except asyncio.CancelledError:
                            logger.info(f"🛑 Graph producer cancelled for session: {session_id}")
                            raise  # 重新抛出CancelledError
                        except Exception as e:
                            logger.error(f"Graph producer error for session {session_id}: {e}")
                            await event_queue.put(ErrorEvent(err_code=ErrorCode.InternalError, err_message=str(e)))
                        finally:
                            await event_queue.put(None)  # 结束标志
                    
                    try:
                        # 启动生产者task
                        producer_task = asyncio.create_task(graph_producer())
                        self.active_graph_tasks[session_id] = producer_task
                        
                        # 消费者：从队列中取事件并yield
                        while True:
                            # 检查停止标志
                            latest_state = self._get_session_state(session_id)
                            if latest_state.get('stop_requested'):
                                logger.info(f"🛑 Stop requested, cancelling producer and clearing queue for session {session_id}")
                                
                                # 1. 立即取消生产者
                                producer_task.cancel()
                                
                                # 2. 清空队列中的所有旧事件
                                queue_cleared_count = 0
                                while not event_queue.empty():
                                    try:
                                        event_queue.get_nowait()
                                        queue_cleared_count += 1
                                    except asyncio.QueueEmpty:
                                        break
                                
                                if queue_cleared_count > 0:
                                    logger.info(f"🗑️ Cleared {queue_cleared_count} pending events from queue for session {session_id}")
                                
                                # 3. 立即返回停止消息
                                yield MessageEvent(content="任务已根据您的请求停止。")
                                current_state['final_output_for_user'] = "任务已根据您的请求停止。"
                                current_state['stop_requested'] = False
                                self._save_session_state(session_id, current_state)
                                return
                            
                            try:
                                # 带超时的获取事件，避免永久阻塞
                                event = await asyncio.wait_for(event_queue.get(), timeout=0.1)
                                if event is None:  # 结束标志
                                    break
                                yield event
                            except asyncio.TimeoutError:
                                continue  # 继续检查停止标志
                    except asyncio.CancelledError:
                        logger.info(f"🛑 Graph stream cancelled for session: {session_id}")
                        yield MessageEvent(content="任务已根据您的请求停止。")
                        current_state['final_output_for_user'] = "任务已根据您的请求停止。"
                        current_state['stop_requested'] = False
                        self._save_session_state(session_id, current_state)
                        return
                    finally:
                        # 清理任务引用
                        if session_id in self.active_graph_tasks:
                            del self.active_graph_tasks[session_id]
                else: 
                    final_user_message = current_state.get('final_output_for_user')
                    if final_user_message: yield MessageEvent(content=final_user_message)
                    self._save_session_state(session_id, current_state)
            else: 
                self._save_session_state(session_id, current_state)

        except Exception as e:
            logger.error(f"Critical error in stream_chat for session {session_id}: {e}", exc_info=True)
            yield ErrorEvent(err_code=ErrorCode.InternalError, err_message=f"An unexpected error occurred: {str(e)}")
            current_state['execution_error'] = str(e)
            self._save_session_state(session_id, current_state)
        finally:
            logger.info(f"Stream ended for session {session_id}")

    # TODO simonhywang remove task list event
    async def _handle_node_updates(self, updates_data, current_state: AgentState):
        for node_name, node_data in updates_data.items():
            # logger.debug(f"AgentService: Processing node '{node_name}' update: {node_data}")
            
            for key, value in node_data.items():
                if key in current_state: 
                    current_state[key] = value

            # 处理planner节点的思考内容
            if node_name == "planner" and 'current_plan' in node_data:
                current_plan = node_data['current_plan']
                if current_plan and isinstance(current_plan, dict) and current_plan.get("subtasks"):
                    # logger.debug(f"AgentService: Planner completed with plan: {current_plan.get('task_name', 'Unknown')}")
                    overall_task_name = current_plan.get("task_name", "Unnamed Plan")
                    all_subtasks_plans = current_plan.get("subtasks", [])
                    
                    task_list_payload = self._build_task_list_event_from_plan(overall_task_name, all_subtasks_plans)
                    # yield TaskListEvent(content=task_list_payload)

            elif node_name == "executor":
                logger.debug("AgentService: Executor related update received.")

        return
                

    def _build_task_list_event_from_plan(self, overall_task_name: str, subtask_plans: List[Dict[str, Any]]) -> Dict[str, Any]:
        step_info_list = []
        for sub_plan in subtask_plans:
            step_info_list.append({
                "id": sub_plan.get("idx"),
                "name": sub_plan.get("desc", "Unnamed Subtask"),
                "status": "pending", 
                "type": "expand", 
                "summary": "pending...",
                "expand": {"title": sub_plan.get("desc"), "status": "pending", "cell_ids": []}
            })
        return {"task_list": [{"id": 1, "name": overall_task_name, "status": "running", "step_info_list": step_info_list}]}

    # TODO flacroxing refactor task category impl
    def _get_task_category(self) -> Optional[str]:
        """获取任务类别"""
        current_state = self._get_session_state(self.session_id)
        slot_state = current_state.get('intent_recognizer_slot_state', {})
        category = slot_state.get('content', {}).get('task', {}).get('category', "")

        return category

    async def _handle_custom_events(self, data):
        logger.info(f"AgentService: 🎯 Processing CUSTOM event data: {data}")

        current_state = self._get_session_state(self.session_id)
        category = get_task_category(current_state)
        ignore_event = category == "document_query"

        # 处理思考事件
        if "thinking_event" in data:
            thinking_data = data["thinking_event"]
            if thinking_data.get("type") == "think":
                content = thinking_data.get("content", "")
                if content:
                    logger.info(f"AgentService: 💭 Yielding ThinkEvent: {content[:50]}...;"
                                f" category: {category}, ignore_event: {ignore_event}")
                    if not ignore_event:
                        yield ThinkEvent(content=content)
        
        # 🎯 新增：处理错误事件
        if "error_event" in data:
            error_data = data["error_event"].get("data", {})
            error_message = error_data.get("error", "Unknown error occurred")
            error_type = error_data.get("error_type", "general_error")
            
            logger.error(f"AgentService: ❌ Processing ErrorEvent: {error_message}, type: {error_type}")
            
            # 发送ErrorEvent
            yield ErrorEvent(err_code=ErrorCode.InternalError, err_message=error_message)
            
            # 更新session状态
            current_state['execution_error'] = error_message
            current_state['status'] = 'execution_error'
            self._save_session_state(self.session_id, current_state)
            
            logger.info(f"AgentService: ✅ ErrorEvent processed and state updated")
        
        # 处理TaskList事件
        if "task_list_event" in data:
            task_list_data = data["task_list_event"]
            logger.info(f"AgentService: 📋 Processing TaskList event: {task_list_data}")
            
            # 提取事件数据
            event_data = task_list_data.get("data", {})
            if event_data:
                logger.info(f"AgentService: ✅ Yielding TaskListEvent from custom event;"
                            f" category: {category}, ignore_event: {ignore_event}")
                if not ignore_event:
                    yield TaskListEvent(content=event_data)
            else:
                logger.warning(f"AgentService: ❌ TaskList event data is empty: {task_list_data}")
        
        # 处理Jupyter事件
        if "jupyter_event" in data:
            jupyter_data = data["jupyter_event"]
            logger.info(f"AgentService: 📊 Processing Jupyter event: {jupyter_data}")
            
            # 构建JupyterEvent参数，只包含实际存在的字段
            jupyter_params = {
                "cell_type": jupyter_data.get("cell_type", "code"),
                "execution_count": jupyter_data.get("execution_count", 0),
                "cell_id": jupyter_data.get("cell_id"),
                # "status": jupyter_data.get("status", "running"),
                "metadata": jupyter_data.get("metadata")
            }
            
            # 只有当字段确实存在且有实际内容时才添加
            if "source" in jupyter_data and jupyter_data["source"]:
                jupyter_params["source"] = jupyter_data["source"]
            if "outputs" in jupyter_data and jupyter_data["outputs"]:
                jupyter_params["outputs"] = jupyter_data["outputs"]
            
            # 将数据转换为JupyterEvent
            jupyter_event = JupyterEvent(**jupyter_params)
            jupyter_event.set_ctx_info(self.session_id, self.record_id, jupyter_data.get("task_id", 1))
            logger.info(f"AgentService: ✅ Yielding JupyterEvent: {jupyter_event.id};"
                        f" category: {category}, ignore_event: {ignore_event}")
            if not ignore_event:
                yield jupyter_event

    async def _execute_graph_stream(self, graph_input_state: AgentState, current_state: AgentState, session_id: str):
        """执行graph stream，支持内部停止检查"""
        final_state = None
        
        async for stream_chunk in self.app.astream(
            graph_input_state,
            config={"recursion_limit": 150},
            stream_mode=["values", "updates", "custom"]  
        ):
            # 在每个chunk处理前检查最新的停止标志
            latest_state = self._get_session_state(session_id)
            if latest_state.get('stop_requested'):
                logger.info(f"🛑 Stop requested detected in graph stream for session {session_id}")
                break
                
            # logger.debug(f"AgentService: 🔍 Raw stream chunk type: {type(stream_chunk)}, content: {stream_chunk}")
            
            if isinstance(stream_chunk, tuple) and len(stream_chunk) == 2:
                mode, data = stream_chunk
                # logger.debug(f"AgentService: 📨 Stream mode: {mode}, data: {data}")
                
                if mode == "updates":
                    # logger.debug(f"AgentService: 🔄 Processing updates mode: {data}")
                    await self._handle_node_updates(data, current_state)

                    # 在yield每个事件前检查停止标志
                    latest_state = self._get_session_state(session_id)
                    if latest_state.get('stop_requested'):
                        logger.info(f"🛑 Stop requested during node updates for session {session_id}")
                        return
                        
                elif mode == "values":
                    final_state = data
                    logger.debug(f"AgentService: Updated final_state from values mode")
                    
                elif mode == "custom":
                    logger.info(f"AgentService: Processing CUSTOM event: {data}")
                    async for event_custom in self._handle_custom_events(data):
                        # 在yield每个事件前检查停止标志
                        latest_state = self._get_session_state(session_id)
                        if latest_state.get('stop_requested'):
                            logger.info(f"🛑 Stop requested during custom events for session {session_id}")
                            return
                        logger.info(f"AgentService: ✅ Yielding custom event: {event_custom.event_type}")
                        yield event_custom
            else:
                logger.debug(f"AgentService: 📤 Single mode stream chunk: {stream_chunk}")
                final_state = stream_chunk
        
        # 处理最终状态
        if final_state:
            # 🎯 新增：检查最终状态中的错误信息
            if final_state.get('status') == 'execution_error' or final_state.get('execution_error'):
                error_message = final_state.get('error_message') or final_state.get('execution_error', 'Execution failed')
                logger.error(f"AgentService: ❌ Final state contains execution error: {error_message}")
                
                # 发送ErrorEvent
                yield ErrorEvent(err_code=ErrorCode.InternalError, err_message=error_message)
                
                # 更新当前状态
                current_state['execution_error'] = error_message
                current_state['status'] = 'execution_error'
                self._save_session_state(session_id, current_state)
                return
            
            # 保存最终回复到conversation_history
            final_response_content = None
            
            if final_state.get('final_summary_content'):
                final_response_content = final_state['final_summary_content']
                # 获取最后一个jupyter event的cell_id
                last_cell_id = None
                jupyter_events = final_state.get('jupyter_events', [])
                if jupyter_events:
                    # 倒序遍历，找到第一个cell_type是sql或code的cell_id
                    for event in reversed(jupyter_events):
                        cell_type = event["cell_type"]
                        # 修复：正确从metadata中获取status字段
                        cell_status = event.get("status", "") or event.get("metadata", {}).get("status", "")
                        if cell_type in ['sql', 'code']:
                            logger.info(f"AgentService: Found last {cell_type} cell_id: {last_cell_id}, status: {cell_status}")
                            if cell_status == 'success':
                                last_cell_id = event["cell_id"]

                            break

                yield FinalSummaryEvent(
                    content=final_state['final_summary_content'],
                    cell_id=last_cell_id
                )
            elif final_state.get('final_output_for_user'):
                # 🔧 只保存到conversation_history，不发送MessageEvent（避免重复发送）
                final_response_content = final_state['final_output_for_user']
                logger.info(f"AgentService: Graph produced final_output_for_user (will save to history): {final_response_content[:50]}...")
                # 注意：不在这里发送MessageEvent，因为可能在其他地方已经发送了
            
            # 🔧 保存最终回复到conversation_history（在update之前先保存当前的conversation_history）
            updated_conversation_history = None
            if final_response_content and final_response_content.strip():
                current_conversation = current_state.get('conversation_history', []).copy()
                if not current_conversation or \
                   current_conversation[-1].get("content") != final_response_content or \
                   current_conversation[-1].get("role") != "assistant":
                    current_conversation.append({"role": "assistant", "content": final_response_content})
                    updated_conversation_history = current_conversation
                    logger.info(f"📝 Prepared final response for conversation_history: {final_response_content[:50]}...")
            
            # 更新状态（这会覆盖conversation_history）
            current_state.update(final_state)
            
            # 🔧 在update之后，恢复更新后的conversation_history
            if updated_conversation_history is not None:
                current_state['conversation_history'] = updated_conversation_history
                logger.info(f"📝 Restored updated conversation_history after final_state update")
            
            # 🔧 清理 final_summary_content，因为它已经被保存到 conversation_history 中了
            if current_state.get('final_summary_content'):
                current_state['final_summary_content'] = None
                logger.info(f"🧹 Cleaned up final_summary_content after saving to conversation_history")
            
            # 🧹 统一清理临时状态，为下一轮对话做准备
            self._cleanup_temporary_state(current_state)
            
            self._save_session_state(session_id, current_state)
        else:
            self._save_session_state(session_id, current_state)

    def _cleanup_temporary_state(self, state: AgentState) -> None:
        """统一清理临时状态，为下一轮对话做准备"""
        logger.debug("🧹 Cleaning up temporary state for next conversation turn")
        
        # 清理规划和执行相关的临时状态
        cleanup_keys = [
            'current_plan',
            'jupyter_events', 
            'needs_clarification',
            # 'clarification_question',
            'execution_error',
            'final_output_for_user',
            'user_feedback_on_last_result',
            
            # Intent recognizer 相关状态会在下一轮重新初始化，所以可以清理
            'intent_recognizer_slot_state',
            'identified_intent_name',
            'identified_intent_entities'
        ]
        
        for key in cleanup_keys:
            if key in state and state[key] is not None:
                old_value = state[key]
                state[key] = None
                logger.debug(f"🧹 Cleaned up {key}: {type(old_value).__name__}")
        
        # 特殊处理：jupyter_events 需要设置为空列表而不是 None
        state['jupyter_events'] = []
        
        # 重置状态标志
        state['stop_requested'] = False
        
        logger.debug("🧹 Temporary state cleanup completed")
