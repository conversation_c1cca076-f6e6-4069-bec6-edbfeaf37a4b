
"""
- **data_science**: 机器学习/数据分析任务（预测、分类、聚类、异常检测、推荐、可视化分析）
- **nl_database_query**: 自然语言数据库查询和日志查询（数据检索、统计查询、报表生成、日志查询）
- **document_query**: 基于知识库召回的文档信息回答用户的复杂问题
- **small_talk**: 简单闲聊
"""

all_prompt = {
    "zh": {
        "data_science": """
请根据任务描述、执行步骤和执行统计生成总结，涵盖 SQL 查询执行结果、数据处理过程中的代码，以及数据科学 pipeline 的重要节点：

1. 主要发现：
- 数据分析结果：SQL查询的核心数据、关键指标(数据性状，数据类型， 缺失值 等)
- 模型表现：准确率、召回率等评估指标，以及特征重要性
- 执行情况：数据处理和特征工程的关键输出（仅报告严重错误）
2. 洞察与建议
- 业务洞察：数据发现的业务含义和价值
- 改进建议：基于结果的优化方向和具体措施
- 下一步行动：可执行的短期和长期方案

约束：
- 请不要直接输出 Python 代码和 BASH 命令和注释
- 请注意使用中文回答问题
- 不要在报告中展示完整数据结果
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 保持简洁，避免冗余描述

用户问题和执行结果汇总如下。请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,
        "nl_database_query": """
以下是基于用户问题生成的SQL查询结果。请分析数据并生成以下报告：
** 结果展示用户问题的结果描述，总结，与产出的图、表等**
- 查询完成，结果如下：
xxxxx

1. 主要发现
- 问题背景：简述用户提出的业务问题
- 数据特征：关键指标、趋势模式、分布情况
- 异常识别：显著偏差、异常值或数据质量问题

2. 洞察与建议
- 数据洞察：数据反映的业务现状和潜在原因
- 关键结论：基于统计分析的核心发现
- 行动建议：针对发现的问题或机会的具体建议

输出格式：Markdown格式，突出关键数字和结论。

约束：
- 请注意使用中文回答问题
- 不要尝试在报告中展示完整数据结果
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 保持简洁，避免冗余描述

用户问题和执行结果汇总如下，请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,
        "nl_database_schema": """
以下是基于用户问题获取的数据表 Schema 定义。
请分析这些字段定义，并根据表格中的内容以 Markdown 格式生成以下报告：

1. 结果展示

约束：
- 请注意使用中文回答问题
- 不要添加任何标题，直接从"## 主要发现"开始
- 不要使用markdown代码块标记（```）
- 不要在结尾添加任何注释或说明
- 保持简洁，避免冗余描述

用户问题和执行结果汇总如下，请将总结内容呈现为简洁且结构清晰的 Markdown 格式，便于后续参考和报告。
        """,

        "document_query": """
请根据问题描述和检索召回结果生成回答，包含以下部分：
1. 用户问题简单总结
以第三人称的视角总结用户问题，不要修改用户问题中的限定词和指代词。

2. 回答：
- 根据用户要求，确定回答的个数；
- 只使用置信度高的检索召回结果（参考标准：重排序评分大于0.6）
- 要求回答所有结果时，回答所有置信度高的检索召回结果
- 没有要求回答个数时，回答所有置信度高的检索召回结果
- 要求回答指定个数时，回答指定个数的置信度高的检索召回结果；如果置信度高的检索召回结果不足，只回答置信度高的检索召回结果
- 每个回答总结详细内容为150字左右；如果用户要求具体细节、详细信息等，总结详细内容为300字左右

3. 总结：
- 以检索召回结果为基础，结合相关信息回答用户问题。

## 约束：
- 请注意置信度是内部概念，不要直接回复置信度或置信度数值。
- 请注意使用中文回答问题.
- 重要：如果查找过程出错，总结相关错误信息并输出。
- 重要：不要直接使用样例回答。
- 如果召回内容于用户问题无关，请直接回答"没有找到相关信息"。

## 输出格式：请以 markdown 格式输出回答，
成功样例如下：
```
我已为您查找出<用户问题>相关信息：
### 回答
#### 1. 案件标题：xxx
- **案件号：** xxx
- **案件类型：** xxx
- **内容总结：** xxx

### 总结
xxx。
```



用户问题和检索结果如下，请根据检索内容回答用户问题：
        """,

        "small_talk": """""", # not used
    },

    # TODO flacroxing add english prompt
    "en": {
        "data_science": """
        Please generate a summary based on the task description, execution steps, and execution statistics, covering SQL query results, code in the data processing pipeline, and important nodes in the data science workflow:

        1. Problem Description: Briefly restate the user's question.
        2. SQL Query Results: Summarize the results of each SQL query, including the main tables, key fields, aggregate metrics (e.g., count, sum, average), and filtering conditions.
        3. Overall Workflow: Provide an overview of the task process, listing key steps in order such as data loading, feature selection, modeling, and evaluation. Ignore environment-specific configurations.
        4. Code Execution Summary: Summarize important computation steps or data transformations in the code, with a focus on data cleaning, feature engineering, and model training outputs. Ignore logs.
        5. Exception and Error Handling: Focus only on severe issues such as ERROR/Exception/Fatal/Critical. Ignore if none are present.
        6. Final Results and Conclusions: Based on the analysis or model predictions in the workflow, summarize metrics like accuracy and recall, and provide conclusive insights and recommendations.

        Constraints:
        - Do not directly output Python code, BASH commands, or comments.
        - Please respond in English.

        The following is a summary of the user's question and the execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,

        "nl_database_query": """
        Below is a table generated by querying SQL based on the user's question (please replace with actual table content).
        Please analyze this data and generate a report in Markdown format based on the table:

        1. Problem Description: Briefly restate the user's question.
        2. Major trends and patterns in the data.
        3. Identify any outliers or anomalies in the table, if applicable.
        4. Provide a brief analytical report based on statistical characteristics of the data.

        Constraints:
        - Please respond in English.

        The following is a summary of the user's question and the execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,

        "nl_database_schema": """
        Below is the schema definition of the data tables retrieved based on the user's question.
        Please analyze the field definitions and generate a report in Markdown format based on the table:

        1. Problem Description: Briefly restate the user's question.
        2. Provide examples of field definitions: such as types and field comments for several fields.

        Constraints:
        - Please respond in English.

        The following is a summary of the user's question and the execution results. Please present the summary in a concise and well-structured Markdown format for future reference and reporting.
                """,

        "document_query": """
        Please generate an answer based on the question description and retrieved results, including the following parts:

        1. Brief summary of the user's question.
        2. Answer:
        - Use only high-confidence retrieved results (rerank score > 0.6).
        - List the retrieved results in descending order of confidence. Summarize each result in about 150 characters, or follow the user's instruction if a different summary style is requested.
        3. Summary:
        - Based on the retrieved results, answer the user's question with related information.

        ## Constraints:
        - Confidence is an internal concept; do not explicitly mention scores or numerical values.
        - Make sure the number of answers matches the user's request.
        - If the user does not specify a number or asks for all results, show all high-confidence answers.
        - Please respond in English.
        - Important: If errors occurred during retrieval, summarize and include the error information.
        - Important: Do not use sample or fabricated answers.

        ## Output Format: Please output the answer in Markdown format.
        Here is an example of a successful response:
### Answer
#### 1. Case Title: xxx
- **Case Number:** xxx
- **Case Type:** xxx
- **Summary:** xxx

### Summary
xxx.
The user's question and retrieval results are as follows. Please answer the user's question based on the retrieved content:
        """,

        "small_talk": """""",  # not used
    }
}