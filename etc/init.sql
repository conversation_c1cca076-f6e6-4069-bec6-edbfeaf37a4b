CREATE DATABASE IF NOT EXISTS data_agent;
use data_agent;

CREATE TABLE if not exists user_agent_versions (
                                     sub_account_uin VARCHAR(20) COMMENT '用户ID',
                                     agent_id VARCHAR(20) COMMENT '代理ID',
                                     agent_name VARCHAR(50) NOT NULL COMMENT '用户代理名称',
                                     agent_version VARCHAR(20) NOT NULL COMMENT '版本号',
                                     description TEXT COMMENT '描述信息',
                                     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     deleted_at TIMESTAMP NULL COMMENT '删除时间',
                                     PRIMARY KEY (sub_account_uin, agent_id)  -- 组合主键
) COMMENT '用户代理版本管理表';
CREATE TABLE if not exists user_info (
                           sub_account_uin VARCHAR(20) PRIMARY KEY COMMENT '用户ID',
                           description TEXT COMMENT '描述信息',
                           jupyter_host VARCHAR(255) COMMENT 'Jupyter主机地址',
                           cos_url VARCHAR(255) COMMENT 'Cos 地址',
                           created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           deleted_at TIMESTAMP NULL COMMENT '删除时间'
) COMMENT '用户信息表';
CREATE TABLE if not exists agent_list (
                            agent_id VARCHAR(20) PRIMARY KEY COMMENT '代理ID',
                            agent_name VARCHAR(50) COMMENT '代理名称',
                            agent_type VARCHAR(10) COMMENT '类型',
                            agent_version VARCHAR(20) COMMENT '版本号',
                            description TEXT COMMENT '描述信息',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户代理列表表';
CREATE TABLE if not exists user_session (
                              sub_account_uin VARCHAR(20) COMMENT '用户ID',
                              session_id VARCHAR(255) COMMENT '会话 ID',
                              session_title VARCHAR(255) COMMENT '会话标题',
                              db_info VARCHAR(255) DEFAULT NULL COMMENT 'db信息',
                              run_record TEXT DEFAULT NULL COMMENT '运行中的聊天请求',
                              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              deleted_at TIMESTAMP NULL COMMENT '删除时间',
                              PRIMARY KEY (sub_account_uin, session_id)
) COMMENT '用户会话表';

INSERT IGNORE INTO agent_list (agent_id, agent_name, agent_type, agent_version, description, created_at, updated_at)
VALUES
    ('agent1', 'Agent One', 'Type A', '1.0.0', 'description for Agent One', NOW(), NOW());

CREATE TABLE if not exists knowledge_list
(
    file_id           VARCHAR(255) COMMENT '文件ID',
    app_id            VARCHAR(64)  COMMENT '用户ID',
    knowledge_base_id VARCHAR(64) COMMENT '知识库ID',
    file_name         VARCHAR(255) COMMENT '文件名称',
    file_size         float COMMENT '文件大小',
    status            int COMMENT '状态',
    file_url          text COMMENT '文件 URL',
    type              int COMMENT '知识类型',
    source            int COMMENT '知识类型',
    is_show_case      int COMMENT '是否为官方示例',
    chunk_config      text COMMENT '分片配置',
    create_user       VARCHAR(20) COMMENT '用户ID',
    create_time       TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time       TIMESTAMP NULL COMMENT '更新时间',
    PRIMARY KEY (file_id)
) COMMENT '知识列表';

CREATE TABLE if not exists task_list
(
    task_id           VARCHAR(64) COMMENT '任务ID',
    app_id            VARCHAR(64) COMMENT 'APP ID',
    file_id           VARCHAR(255) COMMENT '文件ID',
    knowledge_base_id VARCHAR(64) COMMENT '知识库ID',
    file_url          text COMMENT '文件 URL',
    task_type         int COMMENT '任务类型 0语义切分 1规则切分',
    status            int COMMENT '任务状态 0初始 1执行中 2成功 3失败',
    node_id           VARCHAR(64) COMMENT '节点ID',
    error_msg         text COMMENT '错误信息',
    task_params       text COMMENT '任务参数',
    task_result       text COMMENT '任务结果',
    create_time       TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time       TIMESTAMP NULL COMMENT '更新时间',
    PRIMARY KEY (task_id)
) COMMENT '任务列表';

CREATE TABLE if not exists distributed_lock (
    lock_name VARCHAR(64) NOT NULL COMMENT '锁名称（唯一）',
    locked_by VARCHAR(64) NOT NULL COMMENT '持有锁的线程标识',
    locked_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加锁时间',
    expired_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '锁超时时间',
    PRIMARY KEY (lock_name)
)COMMENT '分布式锁表';

CREATE TABLE if not exists user_search_config(
    app_id           VARCHAR(64) NOT NULL COMMENT '用户ID',
    search_type      TINYINT     NOT NULL DEFAULT 0 COMMENT '搜索类型：0-混合搜索，1-向量搜索，2-全文搜索',
    recall_num       INT         NOT NULL DEFAULT 10 COMMENT '召回数量最大值',
    embedding_weight FLOAT       NOT NULL DEFAULT 0.5 COMMENT '向量权重配置',
    rerank_status    TINYINT     NOT NULL DEFAULT 0 COMMENT '重排序状态：0-开启，1-关闭',
    create_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (app_id),
    KEY              idx_search_type (search_type),
    KEY              idx_rerank_status (rerank_status)
)COMMENT='用户搜索配置表';

CREATE TABLE if not exists task_nodes (
  node_id           VARCHAR(64) NOT NULL COMMENT '节点唯一标识',
  last_heartbeat    TIMESTAMP   NOT NULL COMMENT '最后心跳时间',
  is_active         TINYINT(1)  NOT NULL DEFAULT 1 COMMENT '节点是否活跃',
  create_time       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (node_id),
  INDEX idx_last_heartbeat (last_heartbeat)
) COMMENT='任务处理节点状态表';

CREATE TABLE if not exists user_count (
    app_id          VARCHAR(20) COMMENT '主用户 ID',
    sub_account_uin VARCHAR(20) COMMENT '子用户 ID',
    session_id      VARCHAR(20) COMMENT '会话 ID',
    record_id       VARCHAR(20) COMMENT '会话记录 ID',
    create_time     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (app_id, sub_account_uin, record_id)
    ) COMMENT='用户计数表';
