from common.share.context import Context
from fastapi import BackgroundTasks
from typing import Optional
from dataclasses import dataclass


@dataclass
class StreamGenerationParams:
    """流式生成参数封装类"""
    ctx: Optional[Context] = None          # 上下文
    question: Optional[str] = None          # 用户问题
    agent_id: Optional[str] = None          # Agent ID
    history_memory: Optional[dict] = None   # 历史记忆
    mcp_instance: Optional[dict] = None
    eg_instance: Optional[dict] = None
    db_table: Optional[list] = None
    record_id: Optional[str] = None
    req_context: Optional[str] = None
    db_info: Optional[str] = None
