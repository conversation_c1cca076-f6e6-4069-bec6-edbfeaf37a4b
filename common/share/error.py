
from enum import Enum


class ErrorStatus(Enum):
    InternalError = 500
    AuthError = 401


class ErrorCode(Enum):
    # for common
    ParamError = "PARAM_ERROR"
    InternalError = "INTERNAL_ERROR"
    ConfigurationError = "CONFIG_ERROR"
    DatabaseError = "DATABASE_ERROR"
    NotFoundError = "NOT_FOUND_ERROR"
    TimeoutError = "TIMEOUT_ERROR"
    
    # for chat
    ChatAgentCreateError = "AGENT_CREATE_ERROR"

    # for knowledge
    KLChunkTypeError = "CHUNK_TYPE_ERROR"

    # for guardrail
    GuardrailNotSafe = "GUARDRAIL_NOT_SAFE"

    # for user count limit
    UserCountLimitExceeded = "USER_COUNT_LIMIT_EXCEEDED"
    SubUserCountLimitExceeded = "SUB_USER_COUNT_LIMIT_EXCEEDED"
    SessionCountLimitExceeded = "SESSION_COUNT_LIMIT_EXCEEDED"
    SessionIsRunning = "SESSION_IS_RUNNING"
    SessionNotRunning = "SESSION_NOT_RUNNING"


ErrMsg = {
    ErrorCode.InternalError: "internal error, please try again later or contact admin",
    ErrorCode.GuardrailNotSafe: "guardrail not safe",
    ErrorCode.ParamError: "param error",
    ErrorCode.ConfigurationError: "configuration error",
    ErrorCode.DatabaseError: "database error",
    ErrorCode.KLChunkTypeError: "chunk type error",
    ErrorCode.ChatAgentCreateError: "agent create error",
    ErrorCode.NotFoundError: "not found",
    ErrorCode.TimeoutError: "timeout, please try again later",
    ErrorCode.UserCountLimitExceeded: "user count limit exceeded",
    ErrorCode.SubUserCountLimitExceeded: "sub user count limit exceeded",
    ErrorCode.SessionCountLimitExceeded: "session count limit exceeded",
    ErrorCode.SessionNotRunning: "There are no currently running chats in the current session",
}


ErrName = {
    ErrorCode.InternalError: "内部错误，请稍后重试或联系管理员",
    ErrorCode.GuardrailNotSafe: "未通过安全防护",
    ErrorCode.ParamError: "参数错误",
    ErrorCode.ConfigurationError: "配置错误",
    ErrorCode.DatabaseError: "数据库错误",
    ErrorCode.KLChunkTypeError: "知识块类型错误",
    ErrorCode.ChatAgentCreateError: "Agent创建失败",
    ErrorCode.NotFoundError: "未找到",
    ErrorCode.TimeoutError: "超时，请稍后重试",
    ErrorCode.UserCountLimitExceeded: "用户数量超限",
    ErrorCode.SubUserCountLimitExceeded: "子用户数量超限",
    ErrorCode.SessionCountLimitExceeded: "会话数量超限",
    ErrorCode.SessionIsRunning: "当前会话有正在运行的聊天",
}


def error_msg(err_code: ErrorCode) -> str:
    return ErrMsg.get(err_code, "internal error, please try again later or contact admin")


def error_name(err_code: ErrorCode) -> str:
    return ErrName.get(err_code, "内部错误，请稍后重试或联系管理员")
